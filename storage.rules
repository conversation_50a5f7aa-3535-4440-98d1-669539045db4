rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // User profile images
    match /users/{userId}/profile/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Lawyer documents and images
    match /lawyers/{lawyerId}/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == lawyerId;
    }
    
    // Appointment documents
    match /appointments/{appointmentId}/{allPaths=**} {
      allow read, write: if request.auth != null && 
        (request.auth.uid in firestore.get(/databases/(default)/documents/appointments/$(appointmentId)).data.participants);
    }
    
    // Public assets
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
