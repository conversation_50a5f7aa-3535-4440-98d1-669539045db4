import { notFound } from 'next/navigation';
import { unstable_setRequestLocale } from 'next-intl/server';

const locales = ['en', 'es'];

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: { locale: string };
}

export default function LocaleLayout({
  children,
  params: { locale }
}: LocaleLayoutProps) {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  unstable_setRequestLocale(locale);

  return children;
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
