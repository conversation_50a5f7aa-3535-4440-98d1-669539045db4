import { useTranslations } from 'next-intl';
import { unstable_setRequestLocale } from 'next-intl/server';
import HomePageProfessional from '@/components/pages/home-page-professional';

interface HomePageProps {
  params: { locale: string };
}

export default function Home({ params: { locale } }: HomePageProps) {
  unstable_setRequestLocale(locale);
  
  return <HomePageProfessional />;
}

export function generateStaticParams() {
  return [{ locale: 'es' }, { locale: 'en' }];
}
