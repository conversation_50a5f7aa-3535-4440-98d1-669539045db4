import { useTranslations } from 'next-intl';
import { unstable_setRequestLocale } from 'next-intl/server';
import HomePage from '@/components/pages/home-page';

interface HomePageProps {
  params: { locale: string };
}

export default function Home({ params: { locale } }: HomePageProps) {
  unstable_setRequestLocale(locale);
  
  return <HomePage />;
}

export function generateStaticParams() {
  return [{ locale: 'es' }, { locale: 'en' }];
}
