# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyCh4pBG5mh5m70DfyrjJdnbfrUED04QUZA
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=delawpr.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=delawpr
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=delawpr.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=************
NEXT_PUBLIC_FIREBASE_APP_ID=1:************:web:2f73d90d67e9cc16e7cdd3
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-Q2Z0MJCLPR

# Firebase Admin (Server-side) - You'll need to get these from Firebase Console > Project Settings > Service Accounts
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PROJECT_ID=delawpr

# Stripe Configuration - You'll need to get these from Stripe Dashboard
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# Email Configuration (SendGrid) - Optional for now
SENDGRID_API_KEY=your_sendgrid_api_key_here
FROM_EMAIL=<EMAIL>

# Google Maps API (for lawyer locations) - Optional for now
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here
