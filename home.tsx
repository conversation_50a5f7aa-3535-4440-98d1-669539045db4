
'use client';

import React, { useState } from 'react';
import {
  Scale,
  Home as HomeIcon,
  Users,
  FileText,
  Shield,
  Building,
  Clock,
  Star,
  Menu,
  X,
  ChevronRight
} from 'lucide-react';

// Types
interface LegalService {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
}

interface Testimonial {
  id: string;
  name: string;
  rating: number;
  content: string;
}

interface Step {
  number: number;
  title: string;
  description: string;
}

// Data
const legalServices: LegalService[] = [
  {
    id: 'criminal',
    title: 'Derecho Penal',
    description: 'Defensa contra cargos criminales, representación en corte y asesoría legal en asuntos penales.',
    icon: <Scale className="h-6 w-6" />,
    href: '/services/criminal'
  },
  {
    id: 'family',
    title: 'Derecho de Familia',
    description: 'Divorcio, custodia de menores, adopción y otros asuntos legales familiares.',
    icon: <Users className="h-6 w-6" />,
    href: '/services/family'
  },
  {
    id: 'real-estate',
    title: 'Derecho Inmobiliario',
    description: 'Transacciones de propiedades, disputas de arrendamiento y contratos inmobiliarios.',
    icon: <HomeIcon className="h-6 w-6" />,
    href: '/services/real-estate'
  },
  {
    id: 'immigration',
    title: 'Derecho de Inmigración',
    description: 'Solicitudes de visa, ciudadanía, defensa contra deportación y apelaciones migratorias.',
    icon: <Shield className="h-6 w-6" />,
    href: '/services/immigration'
  },
  {
    id: 'corporate',
    title: 'Derecho Corporativo',
    description: 'Formación de empresas, contratos, cumplimiento normativo y gobierno corporativo.',
    icon: <Building className="h-6 w-6" />,
    href: '/services/corporate'
  },
  {
    id: 'personal-injury',
    title: 'Lesiones Personales',
    description: 'Reclamos por accidentes, negligencia médica y compensación por lesiones.',
    icon: <FileText className="h-6 w-6" />,
    href: '/services/personal-injury'
  }
];

const testimonials: Testimonial[] = [
  {
    id: '1',
    name: 'María Rodríguez',
    rating: 5,
    content: 'Necesitaba ayuda con un asunto de derecho familiar y encontré el abogado perfecto a través de LegalPR. El proceso fue simple y pude programar una consulta en 24 horas.'
  },
  {
    id: '2',
    name: 'Carlos Méndez',
    rating: 4,
    content: 'Como propietario de una pequeña empresa, necesitaba asesoría legal para mi startup. LegalPR me conectó con un abogado corporativo que entendió mis necesidades y me brindó excelente orientación.'
  },
  {
    id: '3',
    name: 'Sofía Ramírez',
    rating: 5,
    content: 'La función de estimador de costos me ayudó a presupuestar mis necesidades legales. Encontré un abogado bilingüe que hizo todo el proceso cómodo y sin estrés.'
  }
];

const steps: Step[] = [
  {
    number: 1,
    title: 'Buscar un Abogado',
    description: 'Navega por nuestra extensa red de abogados calificados en Puerto Rico según tus necesidades legales específicas.'
  },
  {
    number: 2,
    title: 'Programar Consulta',
    description: 'Reserva una cita directamente a través de nuestra plataforma en un horario que te convenga.'
  },
  {
    number: 3,
    title: 'Obtener Ayuda Legal',
    description: 'Reúnete con tu abogado, discute tu caso y recibe la asistencia legal que necesitas.'
  }
];

// Navigation Component
const Navigation: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <nav className="bg-white shadow-lg fixed w-full z-50 border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <Scale className="h-8 w-8 text-blue-700" />
              <span className="ml-2 text-xl font-bold text-gray-900">LegalPR</span>
            </div>
            <div className="hidden md:ml-8 md:flex md:space-x-8">
              <a href="#" className="border-b-2 border-blue-600 text-blue-600 inline-flex items-center px-1 pt-1 text-sm font-semibold">
                Inicio
              </a>
              <a href="#services" className="border-transparent border-b-2 hover:border-gray-300 text-gray-600 hover:text-gray-900 inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors">
                Servicios
              </a>
              <a href="#how-it-works" className="border-transparent border-b-2 hover:border-gray-300 text-gray-600 hover:text-gray-900 inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors">
                Cómo Funciona
              </a>
              <a href="#testimonials" className="border-transparent border-b-2 hover:border-gray-300 text-gray-600 hover:text-gray-900 inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors">
                Testimonios
              </a>
            </div>
          </div>
          <div className="flex items-center">
            <div className="flex-shrink-0 space-x-3">
              <button className="bg-white hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 border border-gray-300 rounded-lg shadow-sm transition-colors">
                Iniciar Sesión
              </button>
              <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg shadow-sm transition-colors">
                Registrarse
              </button>
            </div>
            <div className="ml-4 md:hidden flex items-center">
              <button
                type="button"
                className="bg-white rounded-md p-2 inline-flex items-center justify-center text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                <span className="sr-only">Abrir menú principal</span>
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
            <a href="#" className="bg-blue-50 border-blue-500 text-blue-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
              Inicio
            </a>
            <a href="#services" className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
              Servicios
            </a>
            <a href="#how-it-works" className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
              Cómo Funciona
            </a>
            <a href="#testimonials" className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">
              Testimonios
            </a>
          </div>
          <div className="pt-4 pb-3 border-t border-gray-200">
            <div className="flex flex-col items-center px-4 space-y-3">
              <button className="w-full bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 border border-gray-400 rounded shadow">
                Iniciar Sesión
              </button>
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 border border-blue-700 rounded shadow">
                Registrarse
              </button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

// Hero Section Component
const HeroSection: React.FC = () => {
  return (
    <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 pt-32 pb-20 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="w-full h-full bg-slate-800 bg-opacity-40"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:flex lg:items-center lg:justify-between">
          <div className="lg:w-1/2">
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              Encuentra el <span className="bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">Experto Legal</span> Correcto en Puerto Rico
            </h1>
            <p className="mt-6 text-xl text-gray-300 leading-relaxed">
              Conéctate con abogados calificados en todo Puerto Rico para todas tus necesidades legales. Simple, transparente y eficiente.
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4">
              <button className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-semibold rounded-lg text-white bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                <Users className="mr-2 h-5 w-5" />
                Buscar Abogado
              </button>
              <button className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-semibold rounded-lg text-blue-600 bg-white hover:bg-gray-50 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1">
                <Scale className="mr-2 h-5 w-5" />
                Únete como Abogado
              </button>
            </div>
          </div>
          <div className="mt-10 lg:mt-0 lg:w-1/2">
            <div className="relative">
              <svg className="w-full max-w-lg mx-auto" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style={{stopColor: '#3b82f6', stopOpacity: 1}} />
                    <stop offset="100%" style={{stopColor: '#2dd4bf', stopOpacity: 1}} />
                  </linearGradient>
                </defs>
                {/* Puerto Rico Map Outline */}
                <path d="M50,200 C50,150 100,100 200,100 C300,100 350,150 400,150 C450,150 450,200 400,250 C350,300 300,300 200,300 C100,300 50,250 50,200 Z" fill="none" stroke="url(#grad1)" strokeWidth="3" />
                {/* Courthouse */}
                <rect x="200" y="150" width="100" height="100" fill="#1e293b" rx="5" />
                <rect x="220" y="170" width="20" height="80" fill="#334155" />
                <rect x="260" y="170" width="20" height="80" fill="#334155" />
                <rect x="190" y="150" width="120" height="20" fill="#334155" rx="5" />
                <rect x="180" y="140" width="140" height="10" fill="#475569" rx="5" />
                {/* Scales of Justice */}
                <line x1="250" y1="80" x2="250" y2="130" stroke="#e2e8f0" strokeWidth="3" />
                <line x1="220" y1="80" x2="280" y2="80" stroke="#e2e8f0" strokeWidth="3" />
                <circle cx="220" cy="90" r="15" fill="#3b82f6" />
                <circle cx="280" cy="90" r="15" fill="#2dd4bf" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Service Card Component
const ServiceCard: React.FC<{ service: LegalService }> = ({ service }) => {
  return (
    <div className="group bg-white overflow-hidden shadow-lg rounded-xl transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl border border-gray-100">
      <div className="px-6 py-8">
        <div className="bg-blue-50 rounded-full w-14 h-14 flex items-center justify-center mb-6 group-hover:bg-blue-100 transition-colors">
          <div className="text-blue-600">
            {service.icon}
          </div>
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-3">{service.title}</h3>
        <p className="text-gray-600 leading-relaxed mb-6">
          {service.description}
        </p>
        <a
          href={service.href}
          className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium transition-colors group"
        >
          Encontrar Abogados
          <ChevronRight className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform" />
        </a>
      </div>
    </div>
  );
};

// Services Section Component
const ServicesSection: React.FC = () => {
  return (
    <section id="services" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Servicios Legales en Puerto Rico
          </h2>
          <p className="mt-6 max-w-3xl text-xl text-gray-600 mx-auto leading-relaxed">
            Explora nuestra amplia gama de servicios legales proporcionados por abogados calificados en todo Puerto Rico.
          </p>
        </div>

        <div className="mt-20 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {legalServices.map((service) => (
            <ServiceCard key={service.id} service={service} />
          ))}
        </div>

        <div className="mt-16 text-center">
          <button className="inline-flex items-center px-8 py-4 border border-transparent text-lg font-semibold rounded-lg shadow-lg text-white bg-blue-600 hover:bg-blue-700 transition-all duration-200 transform hover:-translate-y-1">
            Ver Todos los Servicios
            <ChevronRight className="ml-2 h-5 w-5" />
          </button>
        </div>
      </div>
    </section>
  );
};

// Step Card Component
const StepCard: React.FC<{ step: Step }> = ({ step }) => {
  return (
    <div className="relative bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group hover:-translate-y-1">
      <div className="absolute -top-4 left-8 w-12 h-12 bg-gradient-to-r from-blue-600 to-teal-500 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
        {step.number}
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mt-6 mb-4">{step.title}</h3>
      <p className="text-gray-600 leading-relaxed">
        {step.description}
      </p>
    </div>
  );
};

// How It Works Section Component
const HowItWorksSection: React.FC = () => {
  return (
    <section id="how-it-works" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Cómo Funciona LegalPR
          </h2>
          <p className="mt-6 max-w-3xl text-xl text-gray-600 mx-auto leading-relaxed">
            Encontrar y conectar con el profesional legal adecuado nunca ha sido tan fácil.
          </p>
        </div>

        <div className="mt-20 grid grid-cols-1 gap-8 md:grid-cols-3">
          {steps.map((step) => (
            <StepCard key={step.number} step={step} />
          ))}
        </div>
      </div>
    </section>
  );
};

// Cost Estimator Component
const CostEstimator: React.FC = () => {
  const [serviceType, setServiceType] = useState('');
  const [consultationType, setConsultationType] = useState('');
  const [estimate, setEstimate] = useState<string | null>(null);

  const calculateEstimate = () => {
    if (serviceType && consultationType) {
      // Simple estimation logic - in real app this would be more sophisticated
      const baseRates: Record<string, number> = {
        'criminal': 200,
        'family': 180,
        'real-estate': 160,
        'immigration': 170,
        'corporate': 250,
        'personal-injury': 220
      };

      const multipliers: Record<string, number> = {
        'initial': 1,
        'follow-up': 0.8,
        'document-review': 0.6,
        'court-representation': 1.5
      };

      const baseRate = baseRates[serviceType] || 180;
      const multiplier = multipliers[consultationType] || 1;
      const estimatedRate = Math.round(baseRate * multiplier);

      setEstimate(`$${estimatedRate - 50} - $${estimatedRate + 50} por hora`);
    }
  };

  return (
    <div className="mt-16 bg-white shadow-xl rounded-2xl overflow-hidden border border-gray-100">
      <div className="px-6 py-6 bg-gradient-to-r from-blue-50 to-teal-50">
        <h3 className="text-2xl font-bold text-gray-900">
          Estimador de Costos
        </h3>
        <p className="mt-2 text-gray-600">
          Obtén una estimación de honorarios legales antes de reservar una consulta.
        </p>
      </div>
      <div className="px-6 py-8">
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <label htmlFor="service-type" className="block text-sm font-semibold text-gray-700 mb-2">
              Tipo de Servicio
            </label>
            <select
              id="service-type"
              value={serviceType}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setServiceType(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="">Selecciona un servicio</option>
              <option value="criminal">Derecho Penal</option>
              <option value="family">Derecho de Familia</option>
              <option value="real-estate">Derecho Inmobiliario</option>
              <option value="immigration">Derecho de Inmigración</option>
              <option value="corporate">Derecho Corporativo</option>
              <option value="personal-injury">Lesiones Personales</option>
            </select>
          </div>
          <div>
            <label htmlFor="consultation-type" className="block text-sm font-semibold text-gray-700 mb-2">
              Tipo de Consulta
            </label>
            <select
              id="consultation-type"
              value={consultationType}
              onChange={(e: React.ChangeEvent<HTMLSelectElement>) => setConsultationType(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
              <option value="">Selecciona tipo</option>
              <option value="initial">Consulta Inicial</option>
              <option value="follow-up">Reunión de Seguimiento</option>
              <option value="document-review">Revisión de Documentos</option>
              <option value="court-representation">Representación en Corte</option>
            </select>
          </div>
        </div>
        <div className="mt-8">
          <button
            type="button"
            onClick={calculateEstimate}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-semibold rounded-lg shadow-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
          >
            <Clock className="mr-2 h-5 w-5" />
            Calcular Estimación
          </button>
        </div>
        {estimate && (
          <div className="mt-6">
            <div className="rounded-lg bg-blue-50 p-4 border border-blue-200">
              <div className="flex">
                <div className="flex-shrink-0">
                  <Clock className="h-5 w-5 text-blue-600" />
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-800">
                    Costo estimado: {estimate}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Testimonial Card Component
const TestimonialCard: React.FC<{ testimonial: Testimonial }> = ({ testimonial }) => {
  return (
    <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 border border-gray-100">
      <div className="flex items-center mb-6">
        <div className="flex-shrink-0">
          <div className="h-12 w-12 bg-gray-200 rounded-full flex items-center justify-center">
            <Users className="h-6 w-6 text-gray-600" />
          </div>
        </div>
        <div className="ml-4">
          <h4 className="text-lg font-bold text-gray-900">{testimonial.name}</h4>
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-4 w-4 ${i < testimonial.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
              />
            ))}
          </div>
        </div>
      </div>
      <p className="text-gray-600 leading-relaxed italic">
        "{testimonial.content}"
      </p>
    </div>
  );
};

// Testimonials Section Component
const TestimonialsSection: React.FC = () => {
  return (
    <section id="testimonials" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-4xl font-extrabold text-gray-900 sm:text-5xl">
            Lo Que Dicen Nuestros Clientes
          </h2>
          <p className="mt-6 max-w-3xl text-xl text-gray-600 mx-auto leading-relaxed">
            Escucha de clientes que han encontrado la representación legal perfecta a través de nuestra plataforma.
          </p>
        </div>

        <div className="mt-20 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial) => (
            <TestimonialCard key={testimonial.id} testimonial={testimonial} />
          ))}
        </div>
      </div>
    </section>
  );
};

// CTA Section Component
const CTASection: React.FC = () => {
  return (
    <section className="bg-gradient-to-r from-blue-700 to-blue-800">
      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-20 lg:px-8 lg:flex lg:items-center lg:justify-between">
        <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
          <span className="block">¿Listo para encontrar tu experto legal?</span>
          <span className="block text-blue-200">Únete a nuestra plataforma hoy.</span>
        </h2>
        <div className="mt-8 flex flex-col sm:flex-row gap-4 lg:mt-0 lg:flex-shrink-0">
          <button className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-semibold rounded-lg text-blue-600 bg-white hover:bg-blue-50 shadow-lg transition-all duration-200 transform hover:-translate-y-1">
            <Users className="mr-2 h-5 w-5" />
            Buscar Abogado
          </button>
          <button className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-semibold rounded-lg text-white bg-blue-800 hover:bg-blue-900 shadow-lg transition-all duration-200 transform hover:-translate-y-1">
            <Scale className="mr-2 h-5 w-5" />
            Únete como Abogado
          </button>
        </div>
      </div>
    </section>
  );
};

// Footer Component
const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-8 xl:col-span-1">
            <div className="flex items-center">
              <Scale className="h-8 w-8 text-blue-400" />
              <span className="ml-2 text-xl font-bold text-white">LegalPR</span>
            </div>
            <p className="text-gray-300 text-base">
              Conectándote con los profesionales legales adecuados en Puerto Rico.
            </p>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-400 hover:text-gray-300 transition-colors">
                <span className="sr-only">Facebook</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path fillRule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-300 transition-colors">
                <span className="sr-only">Instagram</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path fillRule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clipRule="evenodd" />
                </svg>
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-300 transition-colors">
                <span className="sr-only">Twitter</span>
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                </svg>
              </a>
            </div>
          </div>
          <div className="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  Servicios
                </h3>
                <ul className="mt-4 space-y-4">
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Derecho Penal</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Derecho de Familia</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Derecho Inmobiliario</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Derecho de Inmigración</a></li>
                </ul>
              </div>
              <div className="mt-12 md:mt-0">
                <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  Soporte
                </h3>
                <ul className="mt-4 space-y-4">
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Precios</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Documentación</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Guías</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Estado de API</a></li>
                </ul>
              </div>
            </div>
            <div className="md:grid md:grid-cols-2 md:gap-8">
              <div>
                <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  Empresa
                </h3>
                <ul className="mt-4 space-y-4">
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Acerca de</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Blog</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Empleos</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Prensa</a></li>
                </ul>
              </div>
              <div className="mt-12 md:mt-0">
                <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">
                  Legal
                </h3>
                <ul className="mt-4 space-y-4">
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Privacidad</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Términos</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Cookies</a></li>
                  <li><a href="#" className="text-base text-gray-300 hover:text-white transition-colors">Licencias</a></li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-12 border-t border-gray-700 pt-8">
          <p className="text-base text-gray-400 xl:text-center">
            &copy; 2024 LegalPR. Todos los derechos reservados.
          </p>
        </div>
      </div>
    </footer>
  );
};

// Main Home Page Component
const Home: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      <HeroSection />
      <ServicesSection />
      <HowItWorksSection />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <CostEstimator />
      </div>
      <TestimonialsSection />
      <CTASection />
      <Footer />
    </div>
  );
};

export default Home;
