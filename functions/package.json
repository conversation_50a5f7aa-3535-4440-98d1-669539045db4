{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.5.0", "stripe": "^14.9.0", "nodemailer": "^6.9.7", "@sendgrid/mail": "^8.1.0", "cors": "^2.8.5", "express": "^4.18.2"}, "devDependencies": {"typescript": "^5.2.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/nodemailer": "^6.4.14"}, "private": true}