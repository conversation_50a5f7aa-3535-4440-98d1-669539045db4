import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { stripe } from './stripe';
import { sendEmail } from './email';

// Initialize Firebase Admin
admin.initializeApp();

// Export all functions
export { onUserCreate } from './auth';
export { onAppointmentCreate, onAppointmentUpdate } from './appointments';
export { onReviewCreate } from './reviews';
export { createStripeAccount, createPaymentIntent } from './stripe';
export { sendNotificationEmail } from './email';

// Health check function
export const healthCheck = functions.https.onRequest((req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'LegalPR Functions',
    version: '1.0.0'
  });
});

// Scheduled function to clean up old data
export const cleanupOldData = functions.pubsub.schedule('every 24 hours').onRun(async (context) => {
  const db = admin.firestore();
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  try {
    // Clean up old notifications
    const oldNotifications = await db.collection('notifications')
      .where('createdAt', '<', thirtyDaysAgo)
      .where('isRead', '==', true)
      .get();

    const batch = db.batch();
    oldNotifications.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Cleaned up ${oldNotifications.size} old notifications`);
  } catch (error) {
    console.error('Error cleaning up old data:', error);
  }
});

// Function to verify lawyer licenses with Puerto Rico Bar Association
export const verifyLawyerLicense = functions.https.onCall(async (data, context) => {
  // Ensure user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { licenseNumber } = data;

  if (!licenseNumber) {
    throw new functions.https.HttpsError('invalid-argument', 'License number is required');
  }

  try {
    // In a real implementation, this would call the PR Bar Association API
    // For now, we'll simulate the verification
    const isValid = await simulateLicenseVerification(licenseNumber);

    if (isValid) {
      // Update lawyer profile with verification status
      const db = admin.firestore();
      await db.collection('lawyers').doc(context.auth.uid).update({
        isVerified: true,
        verificationDate: admin.firestore.FieldValue.serverTimestamp(),
        licenseVerified: true
      });

      return { verified: true, message: 'License verified successfully' };
    } else {
      return { verified: false, message: 'License verification failed' };
    }
  } catch (error) {
    console.error('License verification error:', error);
    throw new functions.https.HttpsError('internal', 'Verification service unavailable');
  }
});

// Simulate license verification (replace with actual API call)
async function simulateLicenseVerification(licenseNumber: string): Promise<boolean> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Simple validation: license number should be 6-8 digits
  const licenseRegex = /^\d{6,8}$/;
  return licenseRegex.test(licenseNumber);
}

// Function to calculate lawyer ratings
export const updateLawyerRating = functions.firestore
  .document('reviews/{reviewId}')
  .onWrite(async (change, context) => {
    const review = change.after.exists ? change.after.data() : null;
    const previousReview = change.before.exists ? change.before.data() : null;

    if (!review) {
      // Review was deleted
      return;
    }

    const lawyerId = review.lawyerId;
    const db = admin.firestore();

    try {
      // Get all reviews for this lawyer
      const reviewsSnapshot = await db.collection('reviews')
        .where('lawyerId', '==', lawyerId)
        .get();

      let totalRating = 0;
      let reviewCount = 0;

      reviewsSnapshot.forEach(doc => {
        const reviewData = doc.data();
        totalRating += reviewData.rating;
        reviewCount++;
      });

      const averageRating = reviewCount > 0 ? totalRating / reviewCount : 0;

      // Update lawyer's rating
      await db.collection('lawyers').doc(lawyerId).update({
        rating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
        reviewCount: reviewCount
      });

      console.log(`Updated rating for lawyer ${lawyerId}: ${averageRating} (${reviewCount} reviews)`);
    } catch (error) {
      console.error('Error updating lawyer rating:', error);
    }
  });
