import * as functions from 'firebase-functions';
import * as sgMail from '@sendgrid/mail';

// Initialize SendGrid
sgMail.setApiKey(functions.config().sendgrid.api_key);

interface EmailData {
  to: string;
  subject: string;
  template: string;
  data: any;
}

export const sendEmail = async (emailData: EmailData): Promise<void> => {
  try {
    const { to, subject, template, data } = emailData;
    
    // Get template content
    const htmlContent = getEmailTemplate(template, data);
    
    const msg = {
      to,
      from: '<EMAIL>',
      subject,
      html: htmlContent,
    };

    await sgMail.send(msg);
    console.log(`Email sent to ${to} with template ${template}`);
  } catch (error) {
    console.error('Error sending email:', error);
    throw error;
  }
};

export const sendNotificationEmail = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { to, subject, template, templateData } = data;

  try {
    await sendEmail({ to, subject, template, data: templateData });
    return { success: true };
  } catch (error) {
    console.error('Error in sendNotificationEmail:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send email');
  }
});

function getEmailTemplate(template: string, data: any): string {
  const baseStyle = `
    <style>
      body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
      .container { max-width: 600px; margin: 0 auto; padding: 20px; }
      .header { background: linear-gradient(90deg, #3b82f6, #2dd4bf); color: white; padding: 20px; text-align: center; }
      .content { padding: 20px; background: #f9f9f9; }
      .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
      .button { display: inline-block; padding: 12px 24px; background: #3b82f6; color: white; text-decoration: none; border-radius: 5px; }
    </style>
  `;

  switch (template) {
    case 'appointment-confirmation-client':
      return `
        ${baseStyle}
        <div class="container">
          <div class="header">
            <h1>LegalPR</h1>
            <h2>Confirmación de Cita</h2>
          </div>
          <div class="content">
            <p>Estimado/a ${data.clientName},</p>
            <p>Tu cita ha sido programada exitosamente con los siguientes detalles:</p>
            <ul>
              <li><strong>Abogado:</strong> ${data.lawyerName}</li>
              <li><strong>Fecha:</strong> ${data.appointmentDate}</li>
              <li><strong>Hora:</strong> ${data.appointmentTime}</li>
              <li><strong>Tipo:</strong> ${data.appointmentType}</li>
              <li><strong>Costo:</strong> $${data.cost}</li>
            </ul>
            <p>Recibirás un recordatorio 24 horas antes de tu cita.</p>
            <p><a href="https://delawpr.com/dashboard/appointments" class="button">Ver Mis Citas</a></p>
          </div>
          <div class="footer">
            <p>© 2024 LegalPR. Todos los derechos reservados.</p>
          </div>
        </div>
      `;

    case 'appointment-notification-lawyer':
      return `
        ${baseStyle}
        <div class="container">
          <div class="header">
            <h1>LegalPR</h1>
            <h2>Nueva Cita Programada</h2>
          </div>
          <div class="content">
            <p>Estimado/a ${data.lawyerName},</p>
            <p>Se ha programado una nueva cita con los siguientes detalles:</p>
            <ul>
              <li><strong>Cliente:</strong> ${data.clientName}</li>
              <li><strong>Fecha:</strong> ${data.appointmentDate}</li>
              <li><strong>Hora:</strong> ${data.appointmentTime}</li>
              <li><strong>Categoría:</strong> ${data.category}</li>
              <li><strong>Descripción:</strong> ${data.description}</li>
            </ul>
            <p>Por favor, confirma o rechaza esta cita desde tu panel de control.</p>
            <p><a href="https://delawpr.com/dashboard/appointments" class="button">Gestionar Citas</a></p>
          </div>
          <div class="footer">
            <p>© 2024 LegalPR. Todos los derechos reservados.</p>
          </div>
        </div>
      `;

    case 'appointment-reminder':
      return `
        ${baseStyle}
        <div class="container">
          <div class="header">
            <h1>LegalPR</h1>
            <h2>Recordatorio de Cita</h2>
          </div>
          <div class="content">
            <p>Estimado/a ${data.clientName},</p>
            <p>Te recordamos que tienes una cita programada para mañana:</p>
            <ul>
              <li><strong>Abogado:</strong> ${data.lawyerName}</li>
              <li><strong>Fecha:</strong> ${data.appointmentDate}</li>
              <li><strong>Hora:</strong> ${data.appointmentTime}</li>
              <li><strong>Tipo:</strong> ${data.meetingType}</li>
              <li><strong>Ubicación:</strong> ${data.location}</li>
            </ul>
            <p>Por favor, asegúrate de estar disponible a la hora programada.</p>
            <p><a href="https://delawpr.com/dashboard/appointments" class="button">Ver Detalles</a></p>
          </div>
          <div class="footer">
            <p>© 2024 LegalPR. Todos los derechos reservados.</p>
          </div>
        </div>
      `;

    case 'appointment-reminder-lawyer':
      return `
        ${baseStyle}
        <div class="container">
          <div class="header">
            <h1>LegalPR</h1>
            <h2>Recordatorio de Cita</h2>
          </div>
          <div class="content">
            <p>Estimado/a ${data.lawyerName},</p>
            <p>Te recordamos que tienes una cita programada para mañana:</p>
            <ul>
              <li><strong>Cliente:</strong> ${data.clientName}</li>
              <li><strong>Fecha:</strong> ${data.appointmentDate}</li>
              <li><strong>Hora:</strong> ${data.appointmentTime}</li>
              <li><strong>Categoría:</strong> ${data.category}</li>
            </ul>
            <p>Por favor, prepárate para la consulta.</p>
            <p><a href="https://delawpr.com/dashboard/appointments" class="button">Ver Detalles</a></p>
          </div>
          <div class="footer">
            <p>© 2024 LegalPR. Todos los derechos reservados.</p>
          </div>
        </div>
      `;

    case 'appointment-status-update':
      return `
        ${baseStyle}
        <div class="container">
          <div class="header">
            <h1>LegalPR</h1>
            <h2>Actualización de Cita</h2>
          </div>
          <div class="content">
            <p>Estimado/a ${data.clientName},</p>
            <p>${data.message}</p>
            <p><strong>Estado:</strong> ${data.status}</p>
            <p><strong>Fecha de la cita:</strong> ${data.appointmentDate}</p>
            <p><a href="https://delawpr.com/dashboard/appointments" class="button">Ver Detalles</a></p>
          </div>
          <div class="footer">
            <p>© 2024 LegalPR. Todos los derechos reservados.</p>
          </div>
        </div>
      `;

    case 'welcome':
      return `
        ${baseStyle}
        <div class="container">
          <div class="header">
            <h1>LegalPR</h1>
            <h2>¡Bienvenido/a!</h2>
          </div>
          <div class="content">
            <p>Estimado/a ${data.name},</p>
            <p>¡Bienvenido/a a LegalPR! Estamos emocionados de tenerte en nuestra plataforma.</p>
            <p>LegalPR es la plataforma líder en Puerto Rico para conectar clientes con abogados calificados.</p>
            <p>Para comenzar:</p>
            <ul>
              <li>Completa tu perfil</li>
              <li>Explora nuestros servicios legales</li>
              <li>Programa tu primera consulta</li>
            </ul>
            <p><a href="https://delawpr.com/dashboard" class="button">Ir al Panel</a></p>
          </div>
          <div class="footer">
            <p>© 2024 LegalPR. Todos los derechos reservados.</p>
          </div>
        </div>
      `;

    default:
      return `
        ${baseStyle}
        <div class="container">
          <div class="header">
            <h1>LegalPR</h1>
          </div>
          <div class="content">
            <p>Mensaje de LegalPR</p>
          </div>
          <div class="footer">
            <p>© 2024 LegalPR. Todos los derechos reservados.</p>
          </div>
        </div>
      `;
  }
}
