import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

export const onReviewCreate = functions.firestore
  .document('reviews/{reviewId}')
  .onCreate(async (snap, context) => {
    const review = snap.data();
    const reviewId = context.params.reviewId;
    const db = admin.firestore();

    try {
      // Get lawyer and client information
      const [lawyerDoc, clientDoc] = await Promise.all([
        db.collection('lawyers').doc(review.lawyerId).get(),
        db.collection('users').doc(review.clientId).get()
      ]);

      const lawyer = lawyerDoc.data();
      const client = clientDoc.data();

      if (!lawyer || !client) {
        console.error('Lawyer or client not found');
        return;
      }

      // Create notification for lawyer
      await db.collection('notifications').add({
        userId: review.lawyerId,
        title: 'Nueva Reseña',
        message: `${client.firstName} ${client.lastName} ha dejado una reseña de ${review.rating} estrellas.`,
        type: 'review',
        isRead: false,
        actionUrl: `/dashboard/reviews/${reviewId}`,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });

      // If rating is 4 or 5 stars, send a thank you notification to client
      if (review.rating >= 4) {
        await db.collection('notifications').add({
          userId: review.clientId,
          title: 'Gracias por tu Reseña',
          message: 'Gracias por tomarte el tiempo de dejar una reseña. Tu opinión nos ayuda a mejorar.',
          type: 'system',
          isRead: false,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        });
      }

      console.log(`Review notification sent for ${reviewId}`);
    } catch (error) {
      console.error('Error processing review creation:', error);
    }
  });

// Function to detect and handle inappropriate reviews
export const moderateReview = functions.firestore
  .document('reviews/{reviewId}')
  .onCreate(async (snap, context) => {
    const review = snap.data();
    const reviewId = context.params.reviewId;
    const db = admin.firestore();

    try {
      // Simple content moderation (in production, use a proper moderation service)
      const inappropriateWords = [
        'spam', 'fake', 'scam', 'fraud', 'terrible', 'awful', 'horrible',
        // Add more words as needed
      ];

      const reviewText = `${review.title} ${review.content}`.toLowerCase();
      const containsInappropriateContent = inappropriateWords.some(word => 
        reviewText.includes(word)
      );

      if (containsInappropriateContent || review.rating === 1) {
        // Flag for manual review
        await db.collection('admin').doc('flagged-reviews').collection('reviews').add({
          reviewId,
          reason: containsInappropriateContent ? 'inappropriate_content' : 'low_rating',
          flaggedAt: admin.firestore.FieldValue.serverTimestamp(),
          reviewed: false,
          ...review
        });

        // Temporarily hide the review
        await snap.ref.update({
          isHidden: true,
          flaggedForReview: true,
          updatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`Review ${reviewId} flagged for moderation`);
      }
    } catch (error) {
      console.error('Error moderating review:', error);
    }
  });

// Function to handle review responses from lawyers
export const onReviewResponse = functions.firestore
  .document('reviews/{reviewId}')
  .onUpdate(async (change, context) => {
    const before = change.before.data();
    const after = change.after.data();
    const reviewId = context.params.reviewId;
    const db = admin.firestore();

    // Check if a response was added
    if (!before.response && after.response) {
      try {
        // Get client information
        const clientDoc = await db.collection('users').doc(after.clientId).get();
        const client = clientDoc.data();

        if (client) {
          // Create notification for client
          await db.collection('notifications').add({
            userId: after.clientId,
            title: 'Respuesta a tu Reseña',
            message: 'El abogado ha respondido a tu reseña.',
            type: 'review',
            isRead: false,
            actionUrl: `/reviews/${reviewId}`,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
          });

          console.log(`Review response notification sent for ${reviewId}`);
        }
      } catch (error) {
        console.error('Error processing review response:', error);
      }
    }
  });

// Scheduled function to send review reminders
export const sendReviewReminders = functions.pubsub.schedule('every 24 hours').onRun(async (context) => {
  const db = admin.firestore();
  const threeDaysAgo = new Date();
  threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

  try {
    // Find completed appointments from 3 days ago without reviews
    const appointmentsSnapshot = await db.collection('appointments')
      .where('status', '==', 'completed')
      .where('scheduledDate', '<=', threeDaysAgo)
      .get();

    const batch = db.batch();
    let reminderCount = 0;

    for (const doc of appointmentsSnapshot.docs) {
      const appointment = doc.data();
      
      // Check if reminder already sent
      if (appointment.reviewReminderSent) {
        continue;
      }

      // Check if review already exists
      const existingReview = await db.collection('reviews')
        .where('appointmentId', '==', doc.id)
        .get();

      if (!existingReview.empty) {
        // Review exists, mark reminder as sent
        batch.update(doc.ref, { reviewReminderSent: true });
        continue;
      }

      // Get client information
      const clientDoc = await db.collection('users').doc(appointment.clientId).get();
      const client = clientDoc.data();

      if (client && client.preferences?.notifications?.email) {
        // Create notification
        const notificationRef = db.collection('notifications').doc();
        batch.set(notificationRef, {
          userId: appointment.clientId,
          title: 'Deja una Reseña',
          message: 'Tu cita fue completada hace unos días. ¿Te gustaría dejar una reseña?',
          type: 'review',
          isRead: false,
          actionUrl: `/appointments/${doc.id}/review`,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        });

        // Mark reminder as sent
        batch.update(doc.ref, { reviewReminderSent: true });
        reminderCount++;
      }
    }

    await batch.commit();
    console.log(`Sent ${reminderCount} review reminders`);
  } catch (error) {
    console.error('Error sending review reminders:', error);
  }
});
