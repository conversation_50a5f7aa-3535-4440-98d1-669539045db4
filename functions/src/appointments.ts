import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { sendEmail } from './email';

export const onAppointmentCreate = functions.firestore
  .document('appointments/{appointmentId}')
  .onCreate(async (snap, context) => {
    const appointment = snap.data();
    const appointmentId = context.params.appointmentId;
    const db = admin.firestore();

    try {
      // Get client and lawyer information
      const [clientDoc, lawyerDoc] = await Promise.all([
        db.collection('users').doc(appointment.clientId).get(),
        db.collection('lawyers').doc(appointment.lawyerId).get()
      ]);

      const client = clientDoc.data();
      const lawyer = lawyerDoc.data();

      if (!client || !lawyer) {
        console.error('Client or lawyer not found');
        return;
      }

      // Create notifications for both parties
      const notifications = [
        {
          userId: appointment.clientId,
          title: 'Cita Programada',
          message: `Tu cita con ${lawyer.firstName} ${lawyer.lastName} ha sido programada para ${appointment.scheduledDate.toDate().toLocaleDateString('es-PR')}.`,
          type: 'appointment',
          isRead: false,
          actionUrl: `/dashboard/appointments/${appointmentId}`,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        },
        {
          userId: appointment.lawyerId,
          title: 'Nueva Cita',
          message: `${client.firstName} ${client.lastName} ha programado una cita contigo para ${appointment.scheduledDate.toDate().toLocaleDateString('es-PR')}.`,
          type: 'appointment',
          isRead: false,
          actionUrl: `/dashboard/appointments/${appointmentId}`,
          createdAt: admin.firestore.FieldValue.serverTimestamp()
        }
      ];

      // Save notifications
      const batch = db.batch();
      notifications.forEach(notification => {
        const notificationRef = db.collection('notifications').doc();
        batch.set(notificationRef, notification);
      });
      await batch.commit();

      // Send email notifications
      if (client.email && client.preferences?.notifications?.email) {
        await sendEmail({
          to: client.email,
          subject: 'Confirmación de Cita - LegalPR',
          template: 'appointment-confirmation-client',
          data: {
            clientName: `${client.firstName} ${client.lastName}`,
            lawyerName: `${lawyer.firstName} ${lawyer.lastName}`,
            appointmentDate: appointment.scheduledDate.toDate().toLocaleDateString('es-PR'),
            appointmentTime: appointment.scheduledDate.toDate().toLocaleTimeString('es-PR'),
            appointmentType: appointment.meetingType,
            cost: appointment.cost
          }
        });
      }

      if (lawyer.email && lawyer.preferences?.notifications?.email) {
        await sendEmail({
          to: lawyer.email,
          subject: 'Nueva Cita Programada - LegalPR',
          template: 'appointment-notification-lawyer',
          data: {
            lawyerName: `${lawyer.firstName} ${lawyer.lastName}`,
            clientName: `${client.firstName} ${client.lastName}`,
            appointmentDate: appointment.scheduledDate.toDate().toLocaleDateString('es-PR'),
            appointmentTime: appointment.scheduledDate.toDate().toLocaleTimeString('es-PR'),
            category: appointment.category,
            description: appointment.description
          }
        });
      }

      console.log(`Appointment notifications sent for ${appointmentId}`);
    } catch (error) {
      console.error('Error processing appointment creation:', error);
    }
  });

export const onAppointmentUpdate = functions.firestore
  .document('appointments/{appointmentId}')
  .onUpdate(async (change, context) => {
    const before = change.before.data();
    const after = change.after.data();
    const appointmentId = context.params.appointmentId;
    const db = admin.firestore();

    // Check if status changed
    if (before.status !== after.status) {
      try {
        // Get client and lawyer information
        const [clientDoc, lawyerDoc] = await Promise.all([
          db.collection('users').doc(after.clientId).get(),
          db.collection('lawyers').doc(after.lawyerId).get()
        ]);

        const client = clientDoc.data();
        const lawyer = lawyerDoc.data();

        if (!client || !lawyer) {
          console.error('Client or lawyer not found');
          return;
        }

        let notificationTitle = '';
        let clientMessage = '';
        let lawyerMessage = '';

        switch (after.status) {
          case 'confirmed':
            notificationTitle = 'Cita Confirmada';
            clientMessage = `Tu cita con ${lawyer.firstName} ${lawyer.lastName} ha sido confirmada.`;
            lawyerMessage = `Has confirmado la cita con ${client.firstName} ${client.lastName}.`;
            break;
          case 'cancelled':
            notificationTitle = 'Cita Cancelada';
            clientMessage = `Tu cita con ${lawyer.firstName} ${lawyer.lastName} ha sido cancelada.`;
            lawyerMessage = `La cita con ${client.firstName} ${client.lastName} ha sido cancelada.`;
            break;
          case 'completed':
            notificationTitle = 'Cita Completada';
            clientMessage = `Tu cita con ${lawyer.firstName} ${lawyer.lastName} ha sido completada. Por favor, deja una reseña.`;
            lawyerMessage = `La cita con ${client.firstName} ${client.lastName} ha sido completada.`;
            break;
        }

        if (notificationTitle) {
          // Create notifications
          const notifications = [
            {
              userId: after.clientId,
              title: notificationTitle,
              message: clientMessage,
              type: 'appointment',
              isRead: false,
              actionUrl: `/dashboard/appointments/${appointmentId}`,
              createdAt: admin.firestore.FieldValue.serverTimestamp()
            },
            {
              userId: after.lawyerId,
              title: notificationTitle,
              message: lawyerMessage,
              type: 'appointment',
              isRead: false,
              actionUrl: `/dashboard/appointments/${appointmentId}`,
              createdAt: admin.firestore.FieldValue.serverTimestamp()
            }
          ];

          // Save notifications
          const batch = db.batch();
          notifications.forEach(notification => {
            const notificationRef = db.collection('notifications').doc();
            batch.set(notificationRef, notification);
          });
          await batch.commit();

          // Send email notifications for important status changes
          if (after.status === 'cancelled' || after.status === 'confirmed') {
            if (client.email && client.preferences?.notifications?.email) {
              await sendEmail({
                to: client.email,
                subject: `${notificationTitle} - LegalPR`,
                template: 'appointment-status-update',
                data: {
                  clientName: `${client.firstName} ${client.lastName}`,
                  lawyerName: `${lawyer.firstName} ${lawyer.lastName}`,
                  status: after.status,
                  appointmentDate: after.scheduledDate.toDate().toLocaleDateString('es-PR'),
                  message: clientMessage
                }
              });
            }
          }
        }

        console.log(`Appointment status updated for ${appointmentId}: ${before.status} -> ${after.status}`);
      } catch (error) {
        console.error('Error processing appointment update:', error);
      }
    }
  });

// Scheduled function to send appointment reminders
export const sendAppointmentReminders = functions.pubsub.schedule('every 1 hours').onRun(async (context) => {
  const db = admin.firestore();
  const now = new Date();
  const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

  try {
    // Find appointments scheduled for tomorrow
    const appointmentsSnapshot = await db.collection('appointments')
      .where('status', '==', 'confirmed')
      .where('scheduledDate', '>=', now)
      .where('scheduledDate', '<=', tomorrow)
      .get();

    const batch = db.batch();
    const emailPromises: Promise<any>[] = [];

    for (const doc of appointmentsSnapshot.docs) {
      const appointment = doc.data();
      
      // Check if reminder already sent
      if (appointment.reminderSent) {
        continue;
      }

      // Get client and lawyer information
      const [clientDoc, lawyerDoc] = await Promise.all([
        db.collection('users').doc(appointment.clientId).get(),
        db.collection('lawyers').doc(appointment.lawyerId).get()
      ]);

      const client = clientDoc.data();
      const lawyer = lawyerDoc.data();

      if (client && lawyer) {
        // Mark reminder as sent
        batch.update(doc.ref, { reminderSent: true });

        // Send reminder emails
        if (client.email && client.preferences?.notifications?.email) {
          emailPromises.push(
            sendEmail({
              to: client.email,
              subject: 'Recordatorio de Cita - LegalPR',
              template: 'appointment-reminder',
              data: {
                clientName: `${client.firstName} ${client.lastName}`,
                lawyerName: `${lawyer.firstName} ${lawyer.lastName}`,
                appointmentDate: appointment.scheduledDate.toDate().toLocaleDateString('es-PR'),
                appointmentTime: appointment.scheduledDate.toDate().toLocaleTimeString('es-PR'),
                meetingType: appointment.meetingType,
                location: appointment.location || 'Video call'
              }
            })
          );
        }

        if (lawyer.email && lawyer.preferences?.notifications?.email) {
          emailPromises.push(
            sendEmail({
              to: lawyer.email,
              subject: 'Recordatorio de Cita - LegalPR',
              template: 'appointment-reminder-lawyer',
              data: {
                lawyerName: `${lawyer.firstName} ${lawyer.lastName}`,
                clientName: `${client.firstName} ${client.lastName}`,
                appointmentDate: appointment.scheduledDate.toDate().toLocaleDateString('es-PR'),
                appointmentTime: appointment.scheduledDate.toDate().toLocaleTimeString('es-PR'),
                category: appointment.category
              }
            })
          );
        }
      }
    }

    await Promise.all([batch.commit(), ...emailPromises]);
    console.log(`Sent ${emailPromises.length} appointment reminders`);
  } catch (error) {
    console.error('Error sending appointment reminders:', error);
  }
});
