import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

export const onUserCreate = functions.auth.user().onCreate(async (user) => {
  const db = admin.firestore();
  
  try {
    // Create user document in Firestore
    await db.collection('users').doc(user.uid).set({
      id: user.uid,
      email: user.email,
      firstName: '',
      lastName: '',
      role: 'client', // Default role
      profileImage: user.photoURL || '',
      phone: user.phoneNumber || '',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      isActive: true,
      emailVerified: user.emailVerified,
      preferences: {
        language: 'es',
        notifications: {
          email: true,
          sms: false,
          push: true
        }
      }
    });

    // Send welcome email
    if (user.email) {
      // This would integrate with your email service
      console.log(`Welcome email should be sent to ${user.email}`);
    }

    console.log(`User document created for ${user.uid}`);
  } catch (error) {
    console.error('Error creating user document:', error);
  }
});

export const onUserDelete = functions.auth.user().onDelete(async (user) => {
  const db = admin.firestore();
  
  try {
    // Delete user document
    await db.collection('users').doc(user.uid).delete();
    
    // If user is a lawyer, delete lawyer profile
    const lawyerDoc = await db.collection('lawyers').doc(user.uid).get();
    if (lawyerDoc.exists) {
      await db.collection('lawyers').doc(user.uid).delete();
    }

    // Clean up user's appointments
    const appointmentsSnapshot = await db.collection('appointments')
      .where('clientId', '==', user.uid)
      .get();
    
    const batch = db.batch();
    appointmentsSnapshot.docs.forEach(doc => {
      batch.update(doc.ref, {
        status: 'cancelled',
        cancelledAt: admin.firestore.FieldValue.serverTimestamp(),
        cancelledBy: 'system',
        cancelReason: 'User account deleted'
      });
    });

    await batch.commit();

    console.log(`User data cleaned up for ${user.uid}`);
  } catch (error) {
    console.error('Error cleaning up user data:', error);
  }
});
