import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import Stripe from 'stripe';

const stripe = new Stripe(functions.config().stripe.secret_key, {
  apiVersion: '2023-10-16',
});

export { stripe };

export const createStripeAccount = functions.https.onCall(async (data, context) => {
  // Ensure user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { email, country = 'US' } = data;
  const userId = context.auth.uid;

  try {
    // Create Stripe Express account
    const account = await stripe.accounts.create({
      type: 'express',
      email,
      country,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
    });

    // Save Stripe account ID to lawyer profile
    const db = admin.firestore();
    await db.collection('lawyers').doc(userId).update({
      stripeAccountId: account.id,
      stripeOnboardingComplete: false,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    return { accountId: account.id };
  } catch (error) {
    console.error('Error creating Stripe account:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create Stripe account');
  }
});

export const createAccountLink = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { accountId, refreshUrl, returnUrl } = data;

  try {
    const accountLink = await stripe.accountLinks.create({
      account: accountId,
      refresh_url: refreshUrl,
      return_url: returnUrl,
      type: 'account_onboarding',
    });

    return { url: accountLink.url };
  } catch (error) {
    console.error('Error creating account link:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create account link');
  }
});

export const createPaymentIntent = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { amount, currency = 'usd', appointmentId, lawyerId } = data;

  try {
    const db = admin.firestore();
    
    // Get lawyer's Stripe account ID
    const lawyerDoc = await db.collection('lawyers').doc(lawyerId).get();
    const lawyer = lawyerDoc.data();

    if (!lawyer || !lawyer.stripeAccountId) {
      throw new functions.https.HttpsError('failed-precondition', 'Lawyer Stripe account not found');
    }

    // Calculate application fee (10% platform fee)
    const applicationFee = Math.round(amount * 0.10);

    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      application_fee_amount: applicationFee,
      transfer_data: {
        destination: lawyer.stripeAccountId,
      },
      metadata: {
        appointmentId,
        lawyerId,
        clientId: context.auth.uid,
      },
    });

    // Update appointment with payment intent ID
    await db.collection('appointments').doc(appointmentId).update({
      paymentIntentId: paymentIntent.id,
      paymentStatus: 'pending',
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    return {
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    };
  } catch (error) {
    console.error('Error creating payment intent:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create payment intent');
  }
});

export const handleStripeWebhook = functions.https.onRequest(async (req, res) => {
  const sig = req.headers['stripe-signature'] as string;
  const endpointSecret = functions.config().stripe.webhook_secret;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    res.status(400).send(`Webhook Error: ${err}`);
    return;
  }

  const db = admin.firestore();

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object as Stripe.PaymentIntent;
        const appointmentId = paymentIntent.metadata.appointmentId;

        if (appointmentId) {
          await db.collection('appointments').doc(appointmentId).update({
            paymentStatus: 'paid',
            paidAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });

          // Create notification for successful payment
          await db.collection('notifications').add({
            userId: paymentIntent.metadata.clientId,
            title: 'Pago Procesado',
            message: 'Tu pago ha sido procesado exitosamente.',
            type: 'payment',
            isRead: false,
            actionUrl: `/dashboard/appointments/${appointmentId}`,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
          });
        }
        break;

      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object as Stripe.PaymentIntent;
        const failedAppointmentId = failedPayment.metadata.appointmentId;

        if (failedAppointmentId) {
          await db.collection('appointments').doc(failedAppointmentId).update({
            paymentStatus: 'failed',
            updatedAt: admin.firestore.FieldValue.serverTimestamp()
          });

          // Create notification for failed payment
          await db.collection('notifications').add({
            userId: failedPayment.metadata.clientId,
            title: 'Error en el Pago',
            message: 'Hubo un problema procesando tu pago. Por favor, intenta nuevamente.',
            type: 'payment',
            isRead: false,
            actionUrl: `/dashboard/appointments/${failedAppointmentId}`,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
          });
        }
        break;

      case 'account.updated':
        const account = event.data.object as Stripe.Account;
        
        // Check if onboarding is complete
        if (account.details_submitted && account.charges_enabled) {
          // Find lawyer with this Stripe account ID
          const lawyersSnapshot = await db.collection('lawyers')
            .where('stripeAccountId', '==', account.id)
            .get();

          if (!lawyersSnapshot.empty) {
            const lawyerDoc = lawyersSnapshot.docs[0];
            await lawyerDoc.ref.update({
              stripeOnboardingComplete: true,
              updatedAt: admin.firestore.FieldValue.serverTimestamp()
            });

            // Create notification
            await db.collection('notifications').add({
              userId: lawyerDoc.id,
              title: 'Configuración de Pagos Completa',
              message: 'Tu cuenta de Stripe ha sido configurada exitosamente. Ya puedes recibir pagos.',
              type: 'system',
              isRead: false,
              actionUrl: '/dashboard/profile',
              createdAt: admin.firestore.FieldValue.serverTimestamp()
            });
          }
        }
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Error handling webhook:', error);
    res.status(500).send('Webhook handler failed');
  }
});

export const refundPayment = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  const { paymentIntentId, reason = 'requested_by_customer' } = data;

  try {
    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
      reason,
    });

    return { refundId: refund.id, status: refund.status };
  } catch (error) {
    console.error('Error creating refund:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create refund');
  }
});
