{"version": 3, "sources": ["../../../src/experimental/testmode/server.ts"], "names": ["interceptTestApis", "wrapRequestHandlerWorker", "wrapRequestHandlerNode", "reader", "url", "req", "header", "name", "h", "headers", "undefined", "originalFetch", "global", "fetch", "restoreFetch", "interceptFetch", "restoreHttpGet", "interceptHttpGet", "handler", "res", "withRequest", "parsedUrl"], "mappings": ";;;;;;;;;;;;;;;;IAuBgBA,iBAAiB;eAAjBA;;IAYAC,wBAAwB;eAAxBA;;IAMAC,sBAAsB;eAAtBA;;;yBAvCoC;uBACrB;yBACE;AAGjC,MAAMC,SAA6C;IACjDC,KAAIC,GAAG;QACL,OAAOA,IAAID,GAAG,IAAI;IACpB;IACAE,QAAOD,GAAG,EAAEE,IAAI;QACd,MAAMC,IAAIH,IAAII,OAAO,CAACF,KAAK;QAC3B,IAAIC,MAAME,aAAaF,MAAM,MAAM;YACjC,OAAO;QACT;QACA,IAAI,OAAOA,MAAM,UAAU;YACzB,OAAOA;QACT;QACA,OAAOA,CAAC,CAAC,EAAE,IAAI;IACjB;AACF;AAEO,SAASR;IACd,MAAMW,gBAAgBC,OAAOC,KAAK;IAClC,MAAMC,eAAeC,IAAAA,qBAAc,EAACJ;IACpC,MAAMK,iBAAiBC,IAAAA,yBAAgB,EAACN;IAExC,WAAW;IACX,OAAO;QACLG;QACAE;IACF;AACF;AAEO,SAASf,yBACdiB,OAA6B;IAE7B,OAAO,CAACb,KAAKc,MAAQC,IAAAA,oBAAW,EAACf,KAAKF,QAAQ,IAAMe,QAAQb,KAAKc;AACnE;AAEO,SAASjB,uBACdgB,OAA2B;IAE3B,OAAO,CAACb,KAAKc,KAAKE,YAChBD,IAAAA,oBAAW,EAACf,KAAKF,QAAQ,IAAMe,QAAQb,KAAKc,KAAKE;AACrD"}