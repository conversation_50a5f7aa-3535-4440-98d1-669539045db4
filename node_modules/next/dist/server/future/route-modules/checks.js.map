{"version": 3, "sources": ["../../../../src/server/future/route-modules/checks.ts"], "names": ["isAppRouteRouteModule", "isAppPageRouteModule", "isPagesRouteModule", "isPagesAPIRouteModule", "routeModule", "definition", "kind", "RouteKind", "APP_ROUTE", "APP_PAGE", "PAGES", "PAGES_API"], "mappings": ";;;;;;;;;;;;;;;;;IASgBA,qBAAqB;eAArBA;;IAMAC,oBAAoB;eAApBA;;IAMAC,kBAAkB;eAAlBA;;IAMAC,qBAAqB;eAArBA;;;2BApBU;AAEnB,SAASH,sBACdI,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACC,SAAS;AAC5D;AAEO,SAASP,qBACdG,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACE,QAAQ;AAC3D;AAEO,SAASP,mBACdE,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACG,KAAK;AACxD;AAEO,SAASP,sBACdC,WAAwB;IAExB,OAAOA,YAAYC,UAAU,CAACC,IAAI,KAAKC,oBAAS,CAACI,SAAS;AAC5D"}