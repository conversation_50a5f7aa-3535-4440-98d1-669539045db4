{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/pages-route-matcher-provider.ts"], "names": ["PagesRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "PAGES_MANIFEST", "normalizers", "PagesNormalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "isAPIRoute", "normalized", "analyze", "BLOCKED_PAGES", "includes", "matchers", "page", "detectedLocale", "push", "PagesLocaleRouteMatcher", "kind", "RouteKind", "PAGES", "bundlePath", "normalize", "filename", "i18n", "locale", "PagesRouteMatcher"], "mappings": ";;;;+BAeaA;;;eAAAA;;;4BAfc;2BACmB;2BACpB;mCAInB;8CAKsC;uBAEZ;AAE1B,MAAMA,kCAAkCC,0DAA4B;IAGzEC,YACEC,OAAe,EACfC,cAA8B,EACbC,aACjB;QACA,KAAK,CAACC,yBAAc,EAAEF;4BAFLC;QAIjB,IAAI,CAACE,WAAW,GAAG,IAAIC,uBAAgB,CAACL;IAC1C;IAEA,MAAgBM,UACdC,QAAkB,EACyB;QAC3C,wEAAwE;QACxE,6BAA6B;QAC7B,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAC3BI,MAAM,CAAC,CAACC,WAAa,CAACC,IAAAA,sBAAU,EAACD,UAClC,wEAAwE;QACxE,mBAAmB;SAClBD,MAAM,CAAC,CAACC;gBAEL;YADF,MAAME,aACJ,EAAA,qBAAA,IAAI,CAACZ,YAAY,qBAAjB,mBAAmBa,OAAO,CAACH,UAAUA,QAAQ,KAAIA;YAEnD,0BAA0B;YAC1B,IAAII,wBAAa,CAACC,QAAQ,CAACH,aAAa,OAAO;YAE/C,OAAO;QACT;QAEF,MAAMI,WAAqC,EAAE;QAC7C,KAAK,MAAMC,QAAQX,UAAW;YAC5B,IAAI,IAAI,CAACN,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEkB,cAAc,EAAER,QAAQ,EAAE,GAAG,IAAI,CAACV,YAAY,CAACa,OAAO,CAACI;gBAE/DD,SAASG,IAAI,CACX,IAAIC,0CAAuB,CAAC;oBAC1BC,MAAMC,oBAAS,CAACC,KAAK;oBACrBb;oBACAO;oBACAO,YAAY,IAAI,CAACtB,WAAW,CAACsB,UAAU,CAACC,SAAS,CAACR;oBAClDS,UAAU,IAAI,CAACxB,WAAW,CAACwB,QAAQ,CAACD,SAAS,CAACpB,QAAQ,CAACY,KAAK;oBAC5DU,MAAM;wBACJC,QAAQV;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASG,IAAI,CACX,IAAIU,oCAAiB,CAAC;oBACpBR,MAAMC,oBAAS,CAACC,KAAK;oBACrB,qDAAqD;oBACrDb,UAAUO;oBACVA;oBACAO,YAAY,IAAI,CAACtB,WAAW,CAACsB,UAAU,CAACC,SAAS,CAACR;oBAClDS,UAAU,IAAI,CAACxB,WAAW,CAACwB,QAAQ,CAACD,SAAS,CAACpB,QAAQ,CAACY,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}