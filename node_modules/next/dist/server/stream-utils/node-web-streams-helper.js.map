{"version": 3, "sources": ["../../../src/server/stream-utils/node-web-streams-helper.ts"], "names": ["cloneTransformStream", "chainStreams", "streamFromString", "streamToString", "createBufferedTransformStream", "renderToInitialFizzStream", "createRootLayoutValidatorStream", "continueFizzStream", "continuePostponedFizzStream", "source", "sourceReader", "readable", "<PERSON><PERSON><PERSON><PERSON>", "clone", "TransformStream", "start", "controller", "done", "value", "read", "enqueue", "transform", "streams", "writable", "promise", "Promise", "resolve", "i", "length", "then", "pipeTo", "preventClose", "catch", "str", "encoder", "TextEncoder", "ReadableStream", "encode", "close", "stream", "buffer", "pipeThrough", "createDecodeTransformStream", "WritableStream", "write", "chunk", "Uint8Array", "pending", "flush", "detached", "Detached<PERSON>romise", "scheduleImmediate", "undefined", "combined", "byteLength", "set", "createInsertedHTMLStream", "getServerInsertedHTML", "html", "ReactDOMServer", "element", "streamOptions", "getTracer", "trace", "AppRenderSpan", "renderToReadableStream", "createHeadInsertionTransformStream", "insert", "inserted", "freezing", "decoder", "TextDecoder", "insertion", "content", "decode", "index", "indexOf", "insertedHeadContent", "slice", "createDeferredSuffixStream", "suffix", "flushed", "createMergedTransformStream", "started", "reader", "err", "error", "createMoveSuffixStream", "foundSuffix", "buf", "before", "after", "assetPrefix", "getTree", "foundHtml", "foundBody", "includes", "missingTags", "push", "JSON", "stringify", "tree", "chainTransformers", "transformers", "transformer", "renderStream", "inlinedDataStream", "isStaticGeneration", "serverInsertedHTMLToHead", "validateRootLayout", "closeTag", "suffixUnclosed", "split", "allReady"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;IAYgBA,oBAAoB;eAApBA;;IAmBAC,YAAY;eAAZA;;IAmBAC,gBAAgB;eAAhBA;;IAUMC,cAAc;eAAdA;;IAmBNC,6BAA6B;eAA7BA;;IAgEAC,yBAAyB;eAAzBA;;IAsOAC,+BAA+B;eAA/BA;;IAsFMC,kBAAkB;eAAlBA;;IAkEAC,2BAA2B;eAA3BA;;;wBA3gBI;2BACI;8BACc;iCACZ;2BACE;AAM3B,SAASR,qBAAqBS,MAAuB;IAC1D,MAAMC,eAAeD,OAAOE,QAAQ,CAACC,SAAS;IAC9C,MAAMC,QAAQ,IAAIC,gBAAgB;QAChC,MAAMC,OAAMC,UAAU;YACpB,MAAO,KAAM;gBACX,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMR,aAAaS,IAAI;gBAC/C,IAAIF,MAAM;oBACR;gBACF;gBACAD,WAAWI,OAAO,CAACF;YACrB;QACF;QACA,wBAAwB;QACxBG,cAAa;IACf;IAEA,OAAOR;AACT;AAEO,SAASZ,aACd,GAAGqB,OAA4B;IAE/B,MAAM,EAAEX,QAAQ,EAAEY,QAAQ,EAAE,GAAG,IAAIT;IAEnC,IAAIU,UAAUC,QAAQC,OAAO;IAC7B,IAAK,IAAIC,IAAI,GAAGA,IAAIL,QAAQM,MAAM,EAAE,EAAED,EAAG;QACvCH,UAAUA,QAAQK,IAAI,CAAC,IACrBP,OAAO,CAACK,EAAE,CAACG,MAAM,CAACP,UAAU;gBAAEQ,cAAcJ,IAAI,IAAIL,QAAQM,MAAM;YAAC;IAEvE;IAEA,0EAA0E;IAC1E,gDAAgD;IAChDJ,QAAQQ,KAAK,CAAC,KAAO;IAErB,OAAOrB;AACT;AAEO,SAAST,iBAAiB+B,GAAW;IAC1C,MAAMC,UAAU,IAAIC;IACpB,OAAO,IAAIC,eAAe;QACxBrB,OAAMC,UAAU;YACdA,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAACJ;YAClCjB,WAAWsB,KAAK;QAClB;IACF;AACF;AAEO,eAAenC,eACpBoC,MAAkC;IAElC,IAAIC,SAAS;IAEb,MAAMD,MACJ,wDAAwD;KACvDE,WAAW,CAACC,IAAAA,yCAA2B,KACvCZ,MAAM,CACL,IAAIa,eAAuB;QACzBC,OAAMC,KAAK;YACTL,UAAUK;QACZ;IACF;IAGJ,OAAOL;AACT;AAEO,SAASpC;IAId,IAAIoC,SAAqB,IAAIM;IAC7B,IAAIC;IAEJ,MAAMC,QAAQ,CAAChC;QACb,yDAAyD;QACzD,IAAI+B,SAAS;QAEb,MAAME,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACFnC,WAAWI,OAAO,CAACoB;gBACnBA,SAAS,IAAIM;YACf,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRC,UAAUK;gBACVH,SAASvB,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIZ,gBAAgB;QACzBO,WAAUwB,KAAK,EAAE7B,UAAU;YACzB,kDAAkD;YAClD,MAAMqC,WAAW,IAAIP,WAAWN,OAAOZ,MAAM,GAAGiB,MAAMS,UAAU;YAChED,SAASE,GAAG,CAACf;YACba,SAASE,GAAG,CAACV,OAAOL,OAAOZ,MAAM;YACjCY,SAASa;YAET,sCAAsC;YACtCL,MAAMhC;QACR;QACAgC;YACE,IAAI,CAACD,SAAS;YAEd,OAAOA,QAAQvB,OAAO;QACxB;IACF;AACF;AAEA,SAASgC,yBACPC,qBAA4C;IAE5C,MAAMvB,UAAU,IAAIC;IACpB,OAAO,IAAIrB,gBAAgB;QACzBO,WAAW,OAAOwB,OAAO7B;YACvB,MAAM0C,OAAO,MAAMD;YACnB,IAAIC,MAAM;gBACR1C,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAACqB;YACpC;YAEA1C,WAAWI,OAAO,CAACyB;QACrB;IACF;AACF;AAEO,SAASxC,0BAA0B,EACxCsD,cAAc,EACdC,OAAO,EACPC,aAAa,EAKd;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CAACC,wBAAa,CAACC,sBAAsB,EAAE,UAC7DN,eAAeM,sBAAsB,CAACL,SAASC;AAEnD;AAEA,SAASK,mCACPC,MAA6B;IAE7B,IAAIC,WAAW;IACf,IAAIC,WAAW;IAEf,MAAMnC,UAAU,IAAIC;IACpB,MAAMmC,UAAU,IAAIC;IAEpB,OAAO,IAAIzD,gBAAgB;QACzB,MAAMO,WAAUwB,KAAK,EAAE7B,UAAU;YAC/B,4DAA4D;YAC5D,IAAIqD,UAAU;gBACZrD,WAAWI,OAAO,CAACyB;gBACnB;YACF;YAEA,MAAM2B,YAAY,MAAML;YACxB,IAAIC,UAAU;gBACZpD,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAACmC;gBAClCxD,WAAWI,OAAO,CAACyB;gBACnBwB,WAAW;YACb,OAAO;gBACL,MAAMI,UAAUH,QAAQI,MAAM,CAAC7B;gBAC/B,MAAM8B,QAAQF,QAAQG,OAAO,CAAC;gBAC9B,IAAID,UAAU,CAAC,GAAG;oBAChB,MAAME,sBACJJ,QAAQK,KAAK,CAAC,GAAGH,SAASH,YAAYC,QAAQK,KAAK,CAACH;oBACtD3D,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAACwC;oBAClCR,WAAW;oBACXD,WAAW;gBACb;YACF;YAEA,IAAI,CAACA,UAAU;gBACbpD,WAAWI,OAAO,CAACyB;YACrB,OAAO;gBACLM,IAAAA,4BAAiB,EAAC;oBAChBkB,WAAW;gBACb;YACF;QACF;QACA,MAAMrB,OAAMhC,UAAU;YACpB,gEAAgE;YAChE,MAAMwD,YAAY,MAAML;YACxB,IAAIK,WAAW;gBACbxD,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAACmC;YACpC;QACF;IACF;AACF;AAEA,2DAA2D;AAC3D,gDAAgD;AAChD,SAASO,2BACPC,MAAc;IAEd,IAAIC,UAAU;IACd,IAAIlC;IAEJ,MAAMb,UAAU,IAAIC;IAEpB,MAAMa,QAAQ,CAAChC;QACb,MAAMiC,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEVE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACFnC,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAAC2C;YACpC,EAAE,OAAM;YACN,6DAA6D;YAC7D,8DAA8D;YAC9D,6CAA6C;YAC/C,SAAU;gBACRjC,UAAUK;gBACVH,SAASvB,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIZ,gBAAgB;QACzBO,WAAUwB,KAAK,EAAE7B,UAAU;YACzBA,WAAWI,OAAO,CAACyB;YAEnB,wCAAwC;YACxC,IAAIoC,SAAS;YAEb,gCAAgC;YAChCA,UAAU;YACVjC,MAAMhC;QACR;QACAgC,OAAMhC,UAAU;YACd,IAAI+B,SAAS,OAAOA,QAAQvB,OAAO;YACnC,IAAIyD,SAAS;YAEb,aAAa;YACbjE,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAAC2C;QACpC;IACF;AACF;AAEA,0EAA0E;AAC1E,0BAA0B;AAC1B,SAASE,4BACP3C,MAAkC;IAElC,IAAI4C,UAAU;IACd,IAAIpC,UAAwC;IAE5C,MAAMhC,QAAQ,CAACC;QACb,MAAMoE,SAAS7C,OAAO3B,SAAS;QAE/B,wBAAwB;QACxB,gEAAgE;QAChE,qEAAqE;QACrE,uEAAuE;QACvE,8DAA8D;QAC9D,aAAa;QACb,MAAMqC,WAAW,IAAIC,gCAAe;QACpCH,UAAUE;QAEV,2EAA2E;QAC3E,4EAA4E;QAC5E,wCAAwC;QACxCE,IAAAA,4BAAiB,EAAC;YAChB,IAAI;gBACF,MAAO,KAAM;oBACX,MAAM,EAAElC,IAAI,EAAEC,KAAK,EAAE,GAAG,MAAMkE,OAAOjE,IAAI;oBACzC,IAAIF,MAAM;oBAEVD,WAAWI,OAAO,CAACF;gBACrB;YACF,EAAE,OAAOmE,KAAK;gBACZrE,WAAWsE,KAAK,CAACD;YACnB,SAAU;gBACRpC,SAASvB,OAAO;YAClB;QACF;IACF;IAEA,OAAO,IAAIZ,gBAAgB;QACzBO,WAAUwB,KAAK,EAAE7B,UAAU;YACzBA,WAAWI,OAAO,CAACyB;YAEnB,6DAA6D;YAC7D,IAAIsC,SAAS;YACbA,UAAU;YAEVpE,MAAMC;QACR;QACAgC;YACE,0EAA0E;YAC1E,wCAAwC;YACxC,IAAI,CAACD,SAAS;YACd,IAAI,CAACoC,SAAS;YAEd,OAAOpC,QAAQvB,OAAO;QACxB;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS+D,uBACPP,MAAc;IAEd,IAAIQ,cAAc;IAElB,MAAMtD,UAAU,IAAIC;IACpB,MAAMmC,UAAU,IAAIC;IAEpB,OAAO,IAAIzD,gBAAgB;QACzBO,WAAUwB,KAAK,EAAE7B,UAAU;YACzB,IAAIwE,aAAa;gBACf,OAAOxE,WAAWI,OAAO,CAACyB;YAC5B;YAEA,MAAM4C,MAAMnB,QAAQI,MAAM,CAAC7B;YAC3B,MAAM8B,QAAQc,IAAIb,OAAO,CAACI;YAC1B,IAAIL,QAAQ,CAAC,GAAG;gBACda,cAAc;gBAEd,uEAAuE;gBACvE,2BAA2B;gBAC3B,IAAIC,IAAI7D,MAAM,KAAKoD,OAAOpD,MAAM,EAAE;oBAChC;gBACF;gBAEA,wCAAwC;gBACxC,MAAM8D,SAASD,IAAIX,KAAK,CAAC,GAAGH;gBAC5B9B,QAAQX,QAAQG,MAAM,CAACqD;gBACvB1E,WAAWI,OAAO,CAACyB;gBAEnB,sEAAsE;gBACtE,qCAAqC;gBACrC,IAAI4C,IAAI7D,MAAM,GAAGoD,OAAOpD,MAAM,GAAG+C,OAAO;oBACtC,uCAAuC;oBACvC,MAAMgB,QAAQF,IAAIX,KAAK,CAACH,QAAQK,OAAOpD,MAAM;oBAC7CiB,QAAQX,QAAQG,MAAM,CAACsD;oBACvB3E,WAAWI,OAAO,CAACyB;gBACrB;YACF,OAAO;gBACL7B,WAAWI,OAAO,CAACyB;YACrB;QACF;QACAG,OAAMhC,UAAU;YACd,uEAAuE;YACvE,mCAAmC;YACnCA,WAAWI,OAAO,CAACc,QAAQG,MAAM,CAAC2C;QACpC;IACF;AACF;AAEO,SAAS1E,gCACdsF,cAAc,EAAE,EAChBC,OAAgC;IAEhC,IAAIC,YAAY;IAChB,IAAIC,YAAY;IAEhB,MAAM7D,UAAU,IAAIC;IACpB,MAAMmC,UAAU,IAAIC;IAEpB,IAAIE,UAAU;IACd,OAAO,IAAI3D,gBAAgB;QACzB,MAAMO,WAAUwB,KAAK,EAAE7B,UAAU;YAC/B,+DAA+D;YAC/D,IAAI,CAAC8E,aAAa,CAACC,WAAW;gBAC5BtB,WAAWH,QAAQI,MAAM,CAAC7B,OAAO;oBAAEN,QAAQ;gBAAK;gBAChD,IAAI,CAACuD,aAAarB,QAAQuB,QAAQ,CAAC,UAAU;oBAC3CF,YAAY;gBACd;gBACA,IAAI,CAACC,aAAatB,QAAQuB,QAAQ,CAAC,UAAU;oBAC3CD,YAAY;gBACd;YACF;YACA/E,WAAWI,OAAO,CAACyB;QACrB;QACAG,OAAMhC,UAAU;YACd,qBAAqB;YACrB,IAAI,CAAC8E,aAAa,CAACC,WAAW;gBAC5BtB,WAAWH,QAAQI,MAAM;gBACzB,IAAI,CAACoB,aAAarB,QAAQuB,QAAQ,CAAC,UAAU;oBAC3CF,YAAY;gBACd;gBACA,IAAI,CAACC,aAAatB,QAAQuB,QAAQ,CAAC,UAAU;oBAC3CD,YAAY;gBACd;YACF;YAEA,uEAAuE;YACvE,cAAc;YACd,MAAME,cAAwB,EAAE;YAChC,IAAI,CAACH,WAAWG,YAAYC,IAAI,CAAC;YACjC,IAAI,CAACH,WAAWE,YAAYC,IAAI,CAAC;YAEjC,IAAID,YAAYrE,MAAM,GAAG,GAAG;gBAC1BZ,WAAWI,OAAO,CAChBc,QAAQG,MAAM,CACZ,CAAC,mDAAmD,EAAE8D,KAAKC,SAAS,CAClE;oBAAEH;oBAAaL,aAAaA,eAAe;oBAAIS,MAAMR;gBAAU,GAC/D,SAAS,CAAC;YAGlB;QACF;IACF;AACF;AAEA,SAASS,kBACP3F,QAA2B,EAC3B4F,YAAyD;IAEzD,IAAIhE,SAAS5B;IACb,KAAK,MAAM6F,eAAeD,aAAc;QACtC,IAAI,CAACC,aAAa;QAElBjE,SAASA,OAAOE,WAAW,CAAC+D;IAC9B;IACA,OAAOjE;AACT;AAmBO,eAAehC,mBACpBkG,YAAiC,EACjC,EACEzB,MAAM,EACN0B,iBAAiB,EACjBC,kBAAkB,EAClBlD,qBAAqB,EACrBmD,wBAAwB,EACxBC,kBAAkB,EACI;IAExB,MAAMC,WAAW;IAEjB,6EAA6E;IAC7E,MAAMC,iBAAiB/B,SAASA,OAAOgC,KAAK,CAACF,UAAU,EAAE,CAAC,EAAE,GAAG;IAE/D,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIH,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOX,kBAAkBG,cAAc;QACrC,qDAAqD;QACrDrG;QAEA,gCAAgC;QAChCqD,yBAAyB,CAACmD,2BACtBpD,yBAAyBC,yBACzB;QAEJ,wBAAwB;QACxBsD,kBAAkB,QAAQA,eAAenF,MAAM,GAAG,IAC9CmD,2BAA2BgC,kBAC3B;QAEJ,+EAA+E;QAC/EL,oBAAoBxB,4BAA4BwB,qBAAqB;QAErE,kDAAkD;QAClDnB,uBAAuBuB;QAEvB,0BAA0B;QAC1B,qFAAqF;QACrF,+EAA+E;QAC/ErD,yBAAyBmD,2BACrB1C,mCAAmCT,yBACnC;QAEJoD,qBACIvG,gCACEuG,mBAAmBjB,WAAW,EAC9BiB,mBAAmBhB,OAAO,IAE5B;KACL;AACH;AAUO,eAAerF,4BACpBiG,YAAiC,EACjC,EACEC,iBAAiB,EACjBC,kBAAkB,EAClBlD,qBAAqB,EACrBmD,wBAAwB,EACO;IAEjC,MAAME,WAAW;IAEjB,2EAA2E;IAC3E,+DAA+D;IAC/D,IAAIH,sBAAsB,cAAcF,cAAc;QACpD,MAAMA,aAAaQ,QAAQ;IAC7B;IAEA,OAAOX,kBAAkBG,cAAc;QACrC,qDAAqD;QACrDrG;QAEA,gCAAgC;QAChCqD,yBAAyB,CAACmD,2BACtBpD,yBAAyBC,yBACzB;QAEJ,+EAA+E;QAC/EiD,oBAAoBxB,4BAA4BwB,qBAAqB;QAErE,kDAAkD;QAClDnB,uBAAuBuB;KACxB;AACH"}