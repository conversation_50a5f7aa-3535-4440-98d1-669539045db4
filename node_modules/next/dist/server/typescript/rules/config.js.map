{"version": 3, "sources": ["../../../../src/server/typescript/rules/config.ts"], "names": ["API_DOCS", "dynamic", "description", "options", "link", "fetchCache", "preferredRegion", "<PERSON><PERSON><PERSON><PERSON>", "value", "parsed", "JSON", "parse", "Array", "isArray", "some", "v", "err", "getHint", "join", "revalidate", "type", "false", "Number", "replace", "dynamicParams", "true", "runtime", "metadata", "visitEntryConfig", "fileName", "position", "callback", "source", "getSource", "ts", "getTs", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "visit", "node", "isPositionInsideNode", "isVariableStatement", "modifiers", "m", "kind", "SyntaxKind", "ExportKeyword", "isVariableDeclarationList", "declarationList", "declaration", "declarations", "text", "name", "getText", "createAutoCompletionOptionName", "sort", "sortText", "ScriptElementKind", "constElement", "kindModifiers", "ScriptElementKindModifier", "exportedModifier", "labelDetails", "data", "exportName", "moduleSpecifier", "createAutoCompletionOptionValue", "apiName", "isString", "startsWith", "insertText", "removeStringQuotes", "string", "unknown", "none", "getAPIDescription", "api", "Object", "entries", "map", "key", "config", "addCompletionsAtPosition", "prior", "entryConfig", "push", "keys", "index", "getQuickInfoAtPosition", "overridden", "initializer", "docsLink", "isStringLiteral", "enumElement", "textSpan", "start", "getStart", "length", "getWidth", "displayParts", "documentation", "getCompletionEntryDetails", "entryName", "content", "getSemanticDiagnosticsForExportVariableStatement", "diagnostics", "isIdentifier", "ALLOWED_EXPORTS", "includes", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "INVALID_ENTRY_EXPORT", "messageText", "displayedValue", "errorMessage", "isInvalid", "isNoSubstitutionTemplateLiteral", "val", "<PERSON><PERSON><PERSON><PERSON>", "filter", "test", "isNumericLiteral", "isPrefixUnaryExpression", "isMinusToken", "operator", "operand", "TrueKeyword", "FalseKeyword", "isArrayLiteralExpression", "stringify", "elements", "e", "isBigIntLiteral", "isObjectLiteralExpression", "isRegularExpressionLiteral", "INVALID_OPTION_VALUE", "LEGACY_CONFIG_EXPORT", "prop", "properties", "isPropertyAssignment", "INVALID_CONFIG_OPTION"], "mappings": "AAAA,4EAA4E;;;;;+BA2f5E;;;eAAA;;;uBApfO;0BAKA;AAGP,MAAMA,WAUF;IACFC,SAAS;QACPC,aACE;QACFC,SAAS;YACP,UACE;YACF,mBACE;YACF,WACE;YACF,kBACE;QACJ;QACAC,MAAM;IACR;IACAC,YAAY;QACVH,aACE;QACFC,SAAS;YACP,oBACE;YACF,mBACE;YACF,sBACE;YACF,UACE;YACF,mBACE;YACF,gBACE;YACF,iBACE;QACJ;QACAC,MAAM;IACR;IACAE,iBAAiB;QACfJ,aACE;QACFC,SAAS;YACP,UACE;YACF,YAAY;YACZ,UAAU;QACZ;QACAC,MAAM;QACNG,SAAS,CAACC;YACR,IAAI;gBACF,MAAMC,SAASC,KAAKC,KAAK,CAACH;gBAC1B,OACE,OAAOC,WAAW,YACjBG,MAAMC,OAAO,CAACJ,WAAW,CAACA,OAAOK,IAAI,CAAC,CAACC,IAAM,OAAOA,MAAM;YAE/D,EAAE,OAAOC,KAAK;gBACZ,OAAO;YACT;QACF;QACAC,SAAS,CAACT;YACR,IAAIA,UAAU,QAAQ,OAAO,CAAC,gCAAgC,CAAC;YAC/D,IAAIA,UAAU,UAAU,OAAO,CAAC,0BAA0B,CAAC;YAC3D,IAAIA,UAAU,QAAQ,OAAO,CAAC,oCAAoC,CAAC;YACnE,IAAII,MAAMC,OAAO,CAACL,QAAQ,OAAO,CAAC,mBAAmB,EAAEA,MAAMU,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1E,IAAI,OAAOV,UAAU,UAAU,OAAO,CAAC,kBAAkB,EAAEA,MAAM,CAAC,CAAC;QACrE;IACF;IACAW,YAAY;QACVjB,aACE;QACFkB,MAAM;QACNjB,SAAS;YACPkB,OACE;YACF,GAAG;YACH,IAAI;QACN;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,WAAWc,OAAOd,MAAMe,OAAO,CAAC,MAAM,QAAQ;QACjE;QACAN,SAAS,CAACT;YACR,OAAO,CAAC,uCAAuC,EAAEA,MAAM,WAAW,CAAC;QACrE;IACF;IACAgB,eAAe;QACbtB,aACE;QACFC,SAAS;YACPsB,MAAM;YACNJ,OACE;QACJ;QACAjB,MAAM;QACNG,SAAS,CAACC;YACR,OAAOA,UAAU,UAAUA,UAAU;QACvC;IACF;IACAkB,SAAS;QACPxB,aACE;QACFC,SAAS;YACP,YAAY;YACZ,UAAU;YACV,uBAAuB;QACzB;QACAC,MAAM;IACR;IACAuB,UAAU;QACRzB,aAAa;QACbE,MAAM;IACR;AACF;AAEA,SAASwB,iBACPC,QAAgB,EAChBC,QAAgB,EAChBC,QAA4E;IAE5E,MAAMC,SAASC,IAAAA,gBAAS,EAACJ;IACzB,IAAIG,QAAQ;QACV,MAAME,KAAKC,IAAAA,YAAK;QAChBD,GAAGE,YAAY,CAACJ,QAAQ,SAASK,MAAMC,IAAI;YACzC,uBAAuB;YACvB,IAAIC,IAAAA,2BAAoB,EAACT,UAAUQ,OAAO;oBAItCA;gBAHF,kBAAkB;gBAClB,IACEJ,GAAGM,mBAAmB,CAACF,WACvBA,kBAAAA,KAAKG,SAAS,qBAAdH,gBAAgBxB,IAAI,CAAC,CAAC4B,IAAMA,EAAEC,IAAI,KAAKT,GAAGU,UAAU,CAACC,aAAa,IAClE;oBACA,IAAIX,GAAGY,yBAAyB,CAACR,KAAKS,eAAe,GAAG;wBACtD,KAAK,MAAMC,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;4BAC3D,IAAIV,IAAAA,2BAAoB,EAACT,UAAUkB,cAAc;gCAC/C,2BAA2B;gCAC3B,MAAME,OAAOF,YAAYG,IAAI,CAACC,OAAO;gCACrCrB,SAASmB,MAAMF;4BACjB;wBACF;oBACF;gBACF;YACF;QACF;IACF;AACF;AAEA,SAASK,+BAA+BC,IAAY,EAAEH,IAAY;IAChE,MAAMjB,KAAKC,IAAAA,YAAK;IAChB,OAAO;QACLgB;QACAI,UAAU,MAAMD;QAChBX,MAAMT,GAAGsB,iBAAiB,CAACC,YAAY;QACvCC,eAAexB,GAAGyB,yBAAyB,CAACC,gBAAgB;QAC5DC,cAAc;YACZ3D,aAAa,CAAC,QAAQ,EAAEiD,KAAK,OAAO,CAAC;QACvC;QACAW,MAAM;YACJC,YAAYZ;YACZa,iBAAiB;QACnB;IACF;AACF;AAEA,SAASC,gCACPX,IAAY,EACZH,IAAY,EACZe,OAAe;IAEf,MAAMhC,KAAKC,IAAAA,YAAK;IAChB,MAAMgC,WAAWhB,KAAKiB,UAAU,CAAC;IACjC,OAAO;QACLjB;QACAkB,YAAYC,IAAAA,yBAAkB,EAACnB;QAC/BI,UAAU,KAAKD;QACfX,MAAMwB,WAAWjC,GAAGsB,iBAAiB,CAACe,MAAM,GAAGrC,GAAGsB,iBAAiB,CAACgB,OAAO;QAC3Ed,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;QAChDZ,cAAc;YACZ3D,aAAa,CAAC,QAAQ,EAAEgE,QAAQ,OAAO,CAAC;QAC1C;QACAJ,MAAM;YACJC,YAAYG;YACZF,iBAAiB;QACnB;IACF;AACF;AAEA,SAASU,kBAAkBC,GAAW;IACpC,OACE3E,QAAQ,CAAC2E,IAAI,CAACzE,WAAW,GACzB,SACA0E,OAAOC,OAAO,CAAC7E,QAAQ,CAAC2E,IAAI,CAACxE,OAAO,IAAI,CAAC,GACtC2E,GAAG,CAAC,CAAC,CAACC,KAAKvE,MAAM,GAAK,CAAC,IAAI,EAAEuE,IAAI,IAAI,EAAEvE,MAAM,CAAC,EAC9CU,IAAI,CAAC;AAEZ;AACA,MAAM8D,SAAS;IACb,8CAA8C;IAC9CC,0BACEpD,QAAgB,EAChBC,QAAgB,EAChBoD,KAAqD;QAErDtD,iBAAiBC,UAAUC,UAAU,CAACqD,aAAanC;YACjD,IAAI,CAAChD,QAAQ,CAACmF,YAAY,EAAE;gBAC1B,IAAI5C,IAAAA,2BAAoB,EAACT,UAAUkB,YAAYG,IAAI,GAAG;oBACpD+B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACrF,UAAU8E,GAAG,CAAC,CAAC3B,MAAMmC;wBAClC,OAAOjC,+BAA+BiC,OAAOnC;oBAC/C;gBAEJ;gBACA;YACF;YAEA+B,MAAML,OAAO,CAACO,IAAI,IACbR,OAAOS,IAAI,CAACrF,QAAQ,CAACmF,YAAY,CAAChF,OAAO,IAAI,CAAC,GAAG2E,GAAG,CACrD,CAAC3B,MAAMmC;gBACL,OAAOrB,gCAAgCqB,OAAOnC,MAAMgC;YACtD;QAGN;IACF;IAEA,mDAAmD;IACnDI,wBAAuB1D,QAAgB,EAAEC,QAAgB;QACvD,MAAMI,KAAKC,IAAAA,YAAK;QAEhB,IAAIqD;QACJ5D,iBAAiBC,UAAUC,UAAU,CAACqD,aAAanC;YACjD,IAAI,CAAChD,QAAQ,CAACmF,YAAY,EAAE;YAE5B,MAAMhC,OAAOH,YAAYG,IAAI;YAC7B,MAAM3C,QAAQwC,YAAYyC,WAAW;YAErC,MAAMC,WAAW;gBACf/C,MAAM;gBACNO,MACE,CAAC,yBAAyB,EAAEiC,YAAY,UAAU,CAAC,GACnDnF,QAAQ,CAACmF,YAAY,CAAC/E,IAAI;YAC9B;YAEA,IAAII,SAAS+B,IAAAA,2BAAoB,EAACT,UAAUtB,QAAQ;oBAO9CR,+BAAAA,uBACEA;gBAPN,iCAAiC;gBACjC,MAAMmE,WAAWjC,GAAGyD,eAAe,CAACnF;gBACpC,MAAM0C,OAAOoB,IAAAA,yBAAkB,EAAC9D,MAAM4C,OAAO;gBAC7C,MAAM2B,MAAMZ,WAAW,CAAC,CAAC,EAAEjB,KAAK,CAAC,CAAC,GAAGA;gBAErC,MAAM3C,UAAUP,QAAQ,CAACmF,YAAY,CAAC5E,OAAO,IACzCP,gCAAAA,CAAAA,wBAAAA,QAAQ,CAACmF,YAAY,EAAC5E,OAAO,qBAA7BP,mCAAAA,uBAAgC+E,OAChC,CAAC,GAAC/E,gCAAAA,QAAQ,CAACmF,YAAY,CAAChF,OAAO,qBAA7BH,6BAA+B,CAAC+E,IAAI;gBAE1C,IAAIxE,SAAS;wBAaHP,gCACAA,+BAAAA;oBAbRwF,aAAa;wBACX7C,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;wBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;wBAChDoB,UAAU;4BACRC,OAAOtF,MAAMuF,QAAQ;4BACrBC,QAAQxF,MAAMyF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BACb;gCACExD,MAAM;gCACNO,MACElD,EAAAA,iCAAAA,QAAQ,CAACmF,YAAY,CAAChF,OAAO,qBAA7BH,8BAA+B,CAAC+E,IAAI,OACpC/E,gCAAAA,CAAAA,yBAAAA,QAAQ,CAACmF,YAAY,EAAClE,OAAO,qBAA7BjB,mCAAAA,wBAAgC+E,SAChC;4BACJ;4BACAW;yBACD;oBACH;gBACF,OAAO;oBACL,qCAAqC;oBACrCF,aAAa;wBACX7C,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;wBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;wBAChDoB,UAAU;4BACRC,OAAOtF,MAAMuF,QAAQ;4BACrBC,QAAQxF,MAAMyF,QAAQ;wBACxB;wBACAC,cAAc,EAAE;wBAChBC,eAAe;4BAACT;yBAAS;oBAC3B;gBACF;YACF,OAAO;gBACL,gCAAgC;gBAChCF,aAAa;oBACX7C,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;oBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;oBAChDoB,UAAU;wBACRC,OAAO3C,KAAK4C,QAAQ;wBACpBC,QAAQ7C,KAAK8C,QAAQ;oBACvB;oBACAC,cAAc,EAAE;oBAChBC,eAAe;wBACb;4BACExD,MAAM;4BACNO,MAAMwB,kBAAkBS;wBAC1B;wBACAO;qBACD;gBACH;YACF;QACF;QACA,OAAOF;IACT;IAEA,iDAAiD;IACjDY,2BACEC,SAAiB,EACjBvC,IAAkC;QAElC,MAAM5B,KAAKC,IAAAA,YAAK;QAChB,IACE2B,QACAA,KAAKE,eAAe,IACpBF,KAAKE,eAAe,CAACI,UAAU,CAAC,oBAChC;YACA,IAAIkC,UAAU;YACd,IAAIxC,KAAKE,eAAe,KAAK,qCAAqC;gBAChEsC,UAAU5B,kBAAkB2B;YAC9B,OAAO;gBACL,MAAMlG,UAAUH,QAAQ,CAAC8D,KAAKC,UAAU,CAAC,CAAC5D,OAAO;gBACjD,IAAI,CAACA,SAAS;gBACdmG,UAAUnG,OAAO,CAACkG,UAAU;YAC9B;YACA,OAAO;gBACLlD,MAAMkD;gBACN1D,MAAMT,GAAGsB,iBAAiB,CAACoC,WAAW;gBACtClC,eAAexB,GAAGyB,yBAAyB,CAACc,IAAI;gBAChDyB,cAAc,EAAE;gBAChBC,eAAe;oBACb;wBACExD,MAAM;wBACNO,MAAMoD;oBACR;iBACD;YACH;QACF;IACF;IAEA,yCAAyC;IACzCC,kDACEvE,MAA2B,EAC3BM,IAAgC;QAEhC,MAAMJ,KAAKC,IAAAA,YAAK;QAEhB,MAAMqE,cAAqC,EAAE;QAE7C,yCAAyC;QACzC,IAAItE,GAAGY,yBAAyB,CAACR,KAAKS,eAAe,GAAG;YACtD,KAAK,MAAMC,eAAeV,KAAKS,eAAe,CAACE,YAAY,CAAE;gBAC3D,MAAME,OAAOH,YAAYG,IAAI;gBAC7B,IAAIjB,GAAGuE,YAAY,CAACtD,OAAO;oBACzB,IAAI,CAACuD,yBAAe,CAACC,QAAQ,CAACxD,KAAKD,IAAI,KAAK,CAAClD,QAAQ,CAACmD,KAAKD,IAAI,CAAC,EAAE;wBAChEsD,YAAYpB,IAAI,CAAC;4BACfwB,MAAM5E;4BACN6E,UAAU3E,GAAG4E,kBAAkB,CAACC,KAAK;4BACrCC,MAAMC,wBAAc,CAACC,oBAAoB;4BACzCC,aAAa,CAAC,CAAC,EAAEhE,KAAKD,IAAI,CAAC,4CAA4C,CAAC;4BACxE4C,OAAO3C,KAAK4C,QAAQ;4BACpBC,QAAQ7C,KAAK8C,QAAQ;wBACvB;oBACF,OAAO,IAAIjG,QAAQ,CAACmD,KAAKD,IAAI,CAAC,EAAE;wBAC9B,8BAA8B;wBAC9B,MAAM1C,QAAQwC,YAAYyC,WAAW;wBACrC,MAAMtF,UAAUH,QAAQ,CAACmD,KAAKD,IAAI,CAAC,CAAC/C,OAAO;wBAE3C,IAAIK,SAASL,SAAS;4BACpB,IAAIiH,iBAAiB;4BACrB,IAAIC,eAAe;4BACnB,IAAIC,YAAY;4BAEhB,IACEpF,GAAGyD,eAAe,CAACnF,UACnB0B,GAAGqF,+BAA+B,CAAC/G,QACnC;oCAQGR,6BAAAA;gCAPH,MAAMwH,MAAM,MAAMlD,IAAAA,yBAAkB,EAAC9D,MAAM4C,OAAO,MAAM;gCACxD,MAAMqE,gBAAgB7C,OAAOS,IAAI,CAAClF,SAASuH,MAAM,CAAC,CAAC3G,IACjD,QAAQ4G,IAAI,CAAC5G;gCAGf,IACE,CAAC0G,cAAcd,QAAQ,CAACa,QACxB,GAACxH,8BAAAA,CAAAA,sBAAAA,QAAQ,CAACmD,KAAKD,IAAI,CAAC,EAAC3C,OAAO,qBAA3BP,iCAAAA,qBAA8BwH,OAC/B;oCACAF,YAAY;oCACZF,iBAAiBI;gCACnB;4BACF,OAAO,IACLtF,GAAG0F,gBAAgB,CAACpH,UACnB0B,GAAG2F,uBAAuB,CAACrH,UAC1B0B,GAAG4F,YAAY,CAAC,AAACtH,MAAcuH,QAAQ,KACtC7F,CAAAA,GAAG0F,gBAAgB,CAAC,AAACpH,MAAcwH,OAAO,CAACrF,IAAI,KAC7CT,GAAGuE,YAAY,CAAC,AAACjG,MAAcwH,OAAO,CAACrF,IAAI,KAC1C,AAACnC,MAAcwH,OAAO,CAACrF,IAAI,CAACS,OAAO,OAAO,UAAU,KACzDlB,GAAGuE,YAAY,CAACjG,UAAUA,MAAM4C,OAAO,OAAO,YAC/C;oCAEKpD,8BAAAA;gCADL,MAAMe,IAAIP,MAAM4C,OAAO;gCACvB,IAAI,GAACpD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACmD,KAAKD,IAAI,CAAC,EAAC3C,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCuG,YAAY;oCACZF,iBAAiBrG;gCACnB;4BACF,OAAO,IACLP,MAAMmC,IAAI,KAAKT,GAAGU,UAAU,CAACqF,WAAW,IACxCzH,MAAMmC,IAAI,KAAKT,GAAGU,UAAU,CAACsF,YAAY,EACzC;oCAEKlI,8BAAAA;gCADL,MAAMe,IAAIP,MAAM4C,OAAO;gCACvB,IAAI,GAACpD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACmD,KAAKD,IAAI,CAAC,EAAC3C,OAAO,qBAA3BP,kCAAAA,sBAA8Be,KAAI;oCACrCuG,YAAY;oCACZF,iBAAiBrG;gCACnB;4BACF,OAAO,IAAImB,GAAGiG,wBAAwB,CAAC3H,QAAQ;oCAG1CR,8BAAAA;gCAFH,MAAMe,IAAIP,MAAM4C,OAAO;gCACvB,IACE,GAACpD,+BAAAA,CAAAA,uBAAAA,QAAQ,CAACmD,KAAKD,IAAI,CAAC,EAAC3C,OAAO,qBAA3BP,kCAAAA,sBACCU,KAAK0H,SAAS,CAAC5H,MAAM6H,QAAQ,CAACvD,GAAG,CAAC,CAACwD,IAAMA,EAAElF,OAAO,QAEpD;oCACAkE,YAAY;oCACZF,iBAAiBrG;gCACnB;4BACF,OAAO,IACL,iBAAiB;4BACjBmB,GAAGqG,eAAe,CAAC/H,UACnB0B,GAAGsG,yBAAyB,CAAChI,UAC7B0B,GAAGuG,0BAA0B,CAACjI,UAC9B0B,GAAG2F,uBAAuB,CAACrH,QAC3B;gCACA8G,YAAY;gCACZF,iBAAiB5G,MAAM4C,OAAO;4BAChC,OAAO;gCACL,8DAA8D;gCAC9DkE,YAAY;gCACZF,iBAAiB5G,MAAM4C,OAAO;gCAC9BiE,eAAe,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAEjE,KAAKD,IAAI,CAAC,0DAA0D,CAAC;4BAC3I;4BAEA,IAAIoE,WAAW;gCACbd,YAAYpB,IAAI,CAAC;oCACfwB,MAAM5E;oCACN6E,UAAU3E,GAAG4E,kBAAkB,CAACC,KAAK;oCACrCC,MAAMC,wBAAc,CAACyB,oBAAoB;oCACzCvB,aACEE,gBACA,CAAC,CAAC,EAAED,eAAe,gCAAgC,EAAEjE,KAAKD,IAAI,CAAC,SAAS,CAAC;oCAC3E4C,OAAOtF,MAAMuF,QAAQ;oCACrBC,QAAQxF,MAAMyF,QAAQ;gCACxB;4BACF;wBACF;oBACF,OAAO,IAAI9C,KAAKD,IAAI,KAAKyF,8BAAoB,EAAE;wBAC7C,gCAAgC;wBAChC,4BAA4B;wBAC5B,MAAMnI,QAAQwC,YAAYyC,WAAW;wBACrC,IAAIjF,SAAS0B,GAAGsG,yBAAyB,CAAChI,QAAQ;4BAChD,KAAK,MAAMoI,QAAQpI,MAAMqI,UAAU,CAAE;gCACnC,IACE3G,GAAG4G,oBAAoB,CAACF,SACxB1G,GAAGuE,YAAY,CAACmC,KAAKzF,IAAI,KACzByF,KAAKzF,IAAI,CAACD,IAAI,KAAK,OACnB;oCACAsD,YAAYpB,IAAI,CAAC;wCACfwB,MAAM5E;wCACN6E,UAAU3E,GAAG4E,kBAAkB,CAACC,KAAK;wCACrCC,MAAMC,wBAAc,CAAC8B,qBAAqB;wCAC1C5B,aAAa,CAAC,0HAA0H,CAAC;wCACzIrB,OAAO8C,KAAK7C,QAAQ;wCACpBC,QAAQ4C,KAAK3C,QAAQ;oCACvB;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAOO;IACT;AACF;MAEA,WAAexB"}