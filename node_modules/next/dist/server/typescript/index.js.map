{"version": 3, "sources": ["../../../src/server/typescript/index.ts"], "names": ["createTSPlugin", "typescript", "ts", "create", "info", "init", "proxy", "Object", "k", "keys", "languageService", "x", "args", "apply", "getCompletionsAtPosition", "fileName", "position", "options", "prior", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "isAppEntryFile", "getIsClientEntry", "serverLayer", "filterCompletionsAtPosition", "metadata", "entryConfig", "addCompletionsAtPosition", "source", "getSource", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "isPositionInsideNode", "isDefaultFunctionExport", "push", "<PERSON><PERSON><PERSON><PERSON>", "getCompletionEntryDetails", "entryName", "formatOptions", "preferences", "data", "entryCompletionEntryDetails", "metadataCompletionEntryDetails", "getQuickInfoAtPosition", "definitions", "getDefinitionAtPosition", "hasDisallowedReactAPIDefinition", "metadataInfo", "overridden", "getSemanticDiagnostics", "isClientEntry", "isAppEntry", "e", "file", "category", "DiagnosticCategory", "Error", "code", "NEXT_TS_ERRORS", "MISPLACED_CLIENT_ENTRY", "isInsideApp", "errorDiagnostic", "errorEntry", "isImportDeclaration", "diagnostics", "getSemanticDiagnosticsForImportDeclaration", "isVariableStatement", "modifiers", "some", "m", "kind", "SyntaxKind", "ExportKeyword", "getSemanticDiagnosticsForExportVariableStatement", "metadataDiagnostics", "getSemanticDiagnosticsForExportVariableStatementInClientEntry", "clientBoundary", "getSemanticDiagnosticsForFunctionExport", "isFunctionDeclaration", "isExportDeclaration", "getSemanticDiagnosticsForExportDeclarationInClientEntry", "getSemanticDiagnosticsForExportDeclaration", "getDefinitionAndBoundSpan", "metadataDefinition"], "mappings": "AAAA;;;;;;;;CAQC;;;;+BAqBYA;;;eAAAA;;;uBAXN;0BACwB;+DAEP;+DACA;8DACC;uEACE;iEACN;8DACE;;;;;;AAGhB,MAAMA,iBAAsD,CAAC,EAClEC,YAAYC,EAAE,EACf;IACC,SAASC,OAAOC,IAAsC;QACpDC,IAAAA,WAAI,EAAC;YACHH;YACAE;QACF;QAEA,0BAA0B;QAC1B,MAAME,QAAQC,OAAOJ,MAAM,CAAC;QAC5B,KAAK,IAAIK,KAAKD,OAAOE,IAAI,CAACL,KAAKM,eAAe,EAAG;YAC/C,MAAMC,IAAI,AAACP,KAAKM,eAAe,AAAQ,CAACF,EAAE;YAC1CF,KAAK,CAACE,EAAE,GAAG,CAAC,GAAGI,OAAoBD,EAAEE,KAAK,CAACT,KAAKM,eAAe,EAAEE;QACnE;QAEA,kBAAkB;QAClBN,MAAMQ,wBAAwB,GAAG,CAC/BC,UACAC,UACAC;YAEA,IAAIC,QAAQd,KAAKM,eAAe,CAACI,wBAAwB,CACvDC,UACAC,UACAC,YACG;gBACHE,oBAAoB;gBACpBC,oBAAoB;gBACpBC,yBAAyB;gBACzBC,SAAS,EAAE;YACb;YACA,IAAI,CAACC,IAAAA,qBAAc,EAACR,WAAW,OAAOG;YAEtC,0BAA0B;YAC1B,IAAI,CAACM,IAAAA,uBAAgB,EAACT,WAAW;gBAC/B,gDAAgD;gBAChDG,MAAMI,OAAO,GAAGG,eAAW,CAACC,2BAA2B,CAACR,MAAMI,OAAO;gBAErE,6CAA6C;gBAC7CJ,QAAQS,iBAAQ,CAACD,2BAA2B,CAC1CX,UACAC,UACAC,SACAC;YAEJ;YAEA,2CAA2C;YAC3CU,eAAW,CAACC,wBAAwB,CAACd,UAAUC,UAAUE;YAEzD,MAAMY,SAASC,IAAAA,gBAAS,EAAChB;YACzB,IAAI,CAACe,QAAQ,OAAOZ;YAEpBhB,GAAG8B,YAAY,CAACF,QAAS,CAACG;gBACxB,uDAAuD;gBACvD,IACEC,IAAAA,2BAAoB,EAAClB,UAAUiB,SAC/BE,IAAAA,8BAAuB,EAACF,OACxB;oBACAf,MAAMI,OAAO,CAACc,IAAI,IACbC,cAAY,CAACvB,wBAAwB,CACtCC,UACAkB,MACAjB;gBAGN;YACF;YAEA,OAAOE;QACT;QAEA,+BAA+B;QAC/BZ,MAAMgC,yBAAyB,GAAG,CAChCvB,UACAC,UACAuB,WACAC,eACAV,QACAW,aACAC;YAEA,MAAMC,8BAA8Bf,eAAW,CAACU,yBAAyB,CACvEC,WACAG;YAEF,IAAIC,6BAA6B,OAAOA;YAExC,MAAMC,iCAAiCjB,iBAAQ,CAACW,yBAAyB,CACvEvB,UACAC,UACAuB,WACAC,eACAV,QACAW,aACAC;YAEF,IAAIE,gCAAgC,OAAOA;YAE3C,OAAOxC,KAAKM,eAAe,CAAC4B,yBAAyB,CACnDvB,UACAC,UACAuB,WACAC,eACAV,QACAW,aACAC;QAEJ;QAEA,aAAa;QACbpC,MAAMuC,sBAAsB,GAAG,CAAC9B,UAAkBC;YAChD,MAAME,QAAQd,KAAKM,eAAe,CAACmC,sBAAsB,CACvD9B,UACAC;YAEF,IAAI,CAACO,IAAAA,qBAAc,EAACR,WAAW,OAAOG;YAEtC,oEAAoE;YACpE,IAAI,CAACM,IAAAA,uBAAgB,EAACT,WAAW;gBAC/B,MAAM+B,cAAc1C,KAAKM,eAAe,CAACqC,uBAAuB,CAC9DhC,UACAC;gBAEF,IACE8B,eACArB,eAAW,CAACuB,+BAA+B,CAACF,cAC5C;oBACA;gBACF;gBAEA,MAAMG,eAAetB,iBAAQ,CAACkB,sBAAsB,CAAC9B,UAAUC;gBAC/D,IAAIiC,cAAc,OAAOA;YAC3B;YAEA,MAAMC,aAAatB,eAAW,CAACiB,sBAAsB,CAAC9B,UAAUC;YAChE,IAAIkC,YAAY,OAAOA;YAEvB,OAAOhC;QACT;QAEA,qCAAqC;QACrCZ,MAAM6C,sBAAsB,GAAG,CAACpC;YAC9B,MAAMG,QAAQd,KAAKM,eAAe,CAACyC,sBAAsB,CAACpC;YAC1D,MAAMe,SAASC,IAAAA,gBAAS,EAAChB;YACzB,IAAI,CAACe,QAAQ,OAAOZ;YAEpB,IAAIkC,gBAAgB;YACpB,MAAMC,aAAa9B,IAAAA,qBAAc,EAACR;YAElC,IAAI;gBACFqC,gBAAgB5B,IAAAA,uBAAgB,EAACT,UAAU;YAC7C,EAAE,OAAOuC,GAAQ;gBACfpC,MAAMkB,IAAI,CAAC;oBACTmB,MAAMzB;oBACN0B,UAAUtD,GAAGuD,kBAAkB,CAACC,KAAK;oBACrCC,MAAMC,wBAAc,CAACC,sBAAsB;oBAC3C,GAAGP,CAAC;gBACN;gBACAF,gBAAgB;YAClB;YAEA,IAAIU,IAAAA,kBAAW,EAAC/C,WAAW;gBACzB,MAAMgD,kBAAkBC,cAAU,CAACb,sBAAsB,CACvDrB,QACAsB;gBAEFlC,MAAMkB,IAAI,IAAI2B;YAChB;YAEA7D,GAAG8B,YAAY,CAACF,QAAS,CAACG;oBAgBtBA,iBAmDAA;gBAlEF,IAAI/B,GAAG+D,mBAAmB,CAAChC,OAAO;oBAChC,aAAa;oBACb,IAAIoB,YAAY;wBACd,IAAI,CAACD,eAAe;4BAClB,oDAAoD;4BACpD,MAAMc,cACJzC,eAAW,CAAC0C,0CAA0C,CACpDrC,QACAG;4BAEJf,MAAMkB,IAAI,IAAI8B;wBAChB;oBACF;gBACF,OAAO,IACLhE,GAAGkE,mBAAmB,CAACnC,WACvBA,kBAAAA,KAAKoC,SAAS,qBAAdpC,gBAAgBqC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKtE,GAAGuE,UAAU,CAACC,aAAa,IAClE;oBACA,mBAAmB;oBACnB,IAAIrB,YAAY;wBACd,yCAAyC;wBACzC,MAAMa,cACJtC,eAAW,CAAC+C,gDAAgD,CAC1D7C,QACAG;wBAEJ,MAAM2C,sBAAsBxB,gBACxBzB,iBAAQ,CAACkD,6DAA6D,CACpE9D,UACAkB,QAEFN,iBAAQ,CAACgD,gDAAgD,CACvD5D,UACAkB;wBAENf,MAAMkB,IAAI,IAAI8B,gBAAgBU;oBAChC;oBAEA,IAAIxB,eAAe;wBACjBlC,MAAMkB,IAAI,IACL0C,uBAAc,CAACH,gDAAgD,CAChE7C,QACAG;oBAGN;gBACF,OAAO,IAAIE,IAAAA,8BAAuB,EAACF,OAAO;oBACxC,8BAA8B;oBAC9B,IAAIoB,YAAY;wBACd,MAAMa,cAAc7B,cAAY,CAACc,sBAAsB,CACrDpC,UACAe,QACAG;wBAEFf,MAAMkB,IAAI,IAAI8B;oBAChB;oBAEA,IAAId,eAAe;wBACjBlC,MAAMkB,IAAI,IACL0C,uBAAc,CAACC,uCAAuC,CACvDjD,QACAG;oBAGN;gBACF,OAAO,IACL/B,GAAG8E,qBAAqB,CAAC/C,WACzBA,mBAAAA,KAAKoC,SAAS,qBAAdpC,iBAAgBqC,IAAI,CAAC,CAACC,IAAMA,EAAEC,IAAI,KAAKtE,GAAGuE,UAAU,CAACC,aAAa,IAClE;oBACA,sBAAsB;oBACtB,IAAIrB,YAAY;wBACd,MAAMuB,sBAAsBxB,gBACxBzB,iBAAQ,CAACkD,6DAA6D,CACpE9D,UACAkB,QAEFN,iBAAQ,CAACgD,gDAAgD,CACvD5D,UACAkB;wBAENf,MAAMkB,IAAI,IAAIwC;oBAChB;oBAEA,IAAIxB,eAAe;wBACjBlC,MAAMkB,IAAI,IACL0C,uBAAc,CAACC,uCAAuC,CACvDjD,QACAG;oBAGN;gBACF,OAAO,IAAI/B,GAAG+E,mBAAmB,CAAChD,OAAO;oBACvC,iBAAiB;oBACjB,IAAIoB,YAAY;wBACd,MAAMuB,sBAAsBxB,gBACxBzB,iBAAQ,CAACuD,uDAAuD,CAC9DnE,UACAkB,QAEFN,iBAAQ,CAACwD,0CAA0C,CACjDpE,UACAkB;wBAENf,MAAMkB,IAAI,IAAIwC;oBAChB;gBACF;YACF;YAEA,OAAO1D;QACT;QAEA,4CAA4C;QAC5CZ,MAAM8E,yBAAyB,GAAG,CAACrE,UAAkBC;YACnD,IAAIO,IAAAA,qBAAc,EAACR,aAAa,CAACS,IAAAA,uBAAgB,EAACT,WAAW;gBAC3D,MAAMsE,qBAAqB1D,iBAAQ,CAACyD,yBAAyB,CAC3DrE,UACAC;gBAEF,IAAIqE,oBAAoB,OAAOA;YACjC;YAEA,OAAOjF,KAAKM,eAAe,CAAC0E,yBAAyB,CAACrE,UAAUC;QAClE;QAEA,OAAOV;IACT;IAEA,OAAO;QAAEH;IAAO;AAClB"}