{"version": 3, "sources": ["../../../../src/server/lib/squoosh/codecs.ts"], "names": ["preprocessors", "codecs", "mozEncWasm", "path", "resolve", "__dirname", "mozDecWasm", "webpEncWasm", "webpDecWasm", "avifEncWasm", "avifDecWasm", "pngEncDecWasm", "pngEncDecInit", "pngEncDec", "default", "fsp", "readFile", "pathify", "oxipngWasm", "oxipngInit", "oxipng", "resizeWasm", "resizeInit", "resize", "rotateWasm", "globalThis", "ImageData", "resizeNameToIndex", "name", "Error", "resizeWithAspect", "input_width", "input_height", "target_width", "target_height", "width", "height", "Math", "round", "description", "instantiate", "buffer", "method", "premultiply", "linearRGB", "imageData", "cleanup", "defaultOptions", "fit<PERSON><PERSON><PERSON>", "rotate", "numRotations", "degrees", "sameDimensions", "size", "instance", "WebAssembly", "memory", "exports", "additionalPagesNeeded", "ceil", "byteLength", "grow", "view", "Uint8ClampedArray", "set", "slice", "mozjpeg", "extension", "detectors", "dec", "instantiateEmscriptenWasm", "mozDec", "enc", "mozEnc", "defaultEncoderOptions", "quality", "baseline", "arithmetic", "progressive", "optimize_coding", "smoothing", "color_space", "quant_table", "trellis_multipass", "trellis_opt_zero", "trellis_opt_table", "trellis_loops", "auto_subsample", "chroma_subsample", "separate_chroma_quality", "chroma_quality", "autoOptimize", "option", "min", "max", "webp", "webpDec", "webpEnc", "target_size", "target_PSNR", "sns_strength", "filter_strength", "filter_sharpness", "filter_type", "partitions", "segments", "pass", "show_compressed", "preprocessing", "autofilter", "partition_limit", "alpha_compression", "alpha_filtering", "alpha_quality", "lossless", "exact", "image_hint", "emulate_jpeg_size", "thread_level", "low_memory", "near_lossless", "use_delta_palette", "use_sharp_yuv", "avif", "avifDec", "avifEnc", "cqLevel", "cqAlphaLevel", "denoiseLevel", "tileColsLog2", "tileRowsLog2", "speed", "subsample", "chromaDeltaQ", "sharpness", "tune", "decode", "encode", "opts", "simplePng", "Uint8Array", "optimise", "level"], "mappings": ";;;;;;;;;;;;;;;IAqIaA,aAAa;eAAbA;;IAkFAC,MAAM;eAANA;;;oBAvNmB;8DACV;iCAC6B;yEAqChC;yEAGA;sEAMC;sEAGA;sEAMA;sEAGA;qEAKO;wEAOH;wEAMA;mEAQF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA9CtB,MAAMC,aAAaC,MAAKC,OAAO,CAACC,WAAW;AAG3C,MAAMC,aAAaH,MAAKC,OAAO,CAACC,WAAW;AAM3C,MAAME,cAAcJ,MAAKC,OAAO,CAACC,WAAW;AAG5C,MAAMG,cAAcL,MAAKC,OAAO,CAACC,WAAW;AAM5C,MAAMI,cAAcN,MAAKC,OAAO,CAACC,WAAW;AAG5C,MAAMK,cAAcP,MAAKC,OAAO,CAACC,WAAW;AAK5C,MAAMM,gBAAgBR,MAAKC,OAAO,CAACC,WAAW;AAC9C,MAAMO,gBAAgB,IACpBC,aAAUC,OAAO,CAACC,YAAG,CAACC,QAAQ,CAACC,IAAAA,wBAAO,EAACN;AAKzC,MAAMO,aAAaf,MAAKC,OAAO,CAACC,WAAW;AAC3C,MAAMc,aAAa,IAAMC,gBAAON,OAAO,CAACC,YAAG,CAACC,QAAQ,CAACC,IAAAA,wBAAO,EAACC;AAK7D,MAAMG,aAAalB,MAAKC,OAAO,CAACC,WAAW;AAC3C,MAAMiB,aAAa,IAAMC,gBAAOT,OAAO,CAACC,YAAG,CAACC,QAAQ,CAACC,IAAAA,wBAAO,EAACI;AAE7D,SAAS;AACT,MAAMG,aAAarB,MAAKC,OAAO,CAACC,WAAW;AAIzCoB,WAAmBC,SAAS,GAAGA,mBAAS;AAE1C,SAASC,kBACPC,IAAqD;IAErD,OAAQA;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,MAAMC,MAAM,CAAC,0BAA0B,EAAED,KAAK,CAAC,CAAC;IACpD;AACF;AAEA,SAASE,iBAAiB,EACxBC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACU;IACvB,IAAI,CAACD,gBAAgB,CAACC,eAAe;QACnC,MAAML,MAAM;IACd;IAEA,IAAII,gBAAgBC,eAAe;QACjC,OAAO;YAAEC,OAAOF;YAAcG,QAAQF;QAAc;IACtD;IAEA,IAAI,CAACD,cAAc;QACjB,OAAO;YACLE,OAAOE,KAAKC,KAAK,CAAC,AAACP,cAAcC,eAAgBE;YACjDE,QAAQF;QACV;IACF;IAEA,OAAO;QACLC,OAAOF;QACPG,QAAQC,KAAKC,KAAK,CAAC,AAACN,eAAeD,cAAeE;IACpD;AACF;AAEO,MAAMjC,gBAAgB;IAC3BuB,QAAQ;QACNK,MAAM;QACNW,aAAa;QACbC,aAAa;YACX,MAAMlB;YACN,OAAO,CACLmB,QACAV,aACAC,cACA,EAAEG,KAAK,EAAEC,MAAM,EAAEM,MAAM,EAAEC,WAAW,EAAEC,SAAS,EAAiB;gBAE9D,CAAA,EAAET,KAAK,EAAEC,MAAM,EAAE,GAAGN,iBAAiB;oBACrCC;oBACAC;oBACAC,cAAcE;oBACdD,eAAeE;gBACjB,EAAC;gBACD,MAAMS,YAAY,IAAInB,mBAAS,CAC7BH,gBAAOA,MAAM,CACXkB,QACAV,aACAC,cACAG,OACAC,QACAT,kBAAkBe,SAClBC,aACAC,YAEFT,OACAC;gBAEFb,gBAAOuB,OAAO;gBACd,OAAOD;YACT;QACF;QACAE,gBAAgB;YACdL,QAAQ;YACRM,WAAW;YACXL,aAAa;YACbC,WAAW;QACb;IACF;IACAK,QAAQ;QACNrB,MAAM;QACNW,aAAa;QACbC,aAAa;YACX,OAAO,OACLC,QACAN,OACAC,QACA,EAAEc,YAAY,EAAiB;gBAE/B,MAAMC,UAAU,AAACD,eAAe,KAAM;gBACtC,MAAME,iBAAiBD,YAAY,KAAKA,YAAY;gBACpD,MAAME,OAAOlB,QAAQC,SAAS;gBAC9B,MAAMkB,WAAW,AACf,CAAA,MAAMC,YAAYf,WAAW,CAAC,MAAMzB,YAAG,CAACC,QAAQ,CAACC,IAAAA,wBAAO,EAACO,aAAY,EACrE8B,QAAQ;gBACV,MAAM,EAAEE,MAAM,EAAE,GAAGF,SAASG,OAAO;gBACnC,MAAMC,wBAAwBrB,KAAKsB,IAAI,CACrC,AAACN,CAAAA,OAAO,IAAIG,OAAOf,MAAM,CAACmB,UAAU,GAAG,CAAA,IAAM,CAAA,KAAK,IAAG;gBAEvD,IAAIF,wBAAwB,GAAG;oBAC7BF,OAAOK,IAAI,CAACH;gBACd;gBACA,MAAMI,OAAO,IAAIC,kBAAkBP,OAAOf,MAAM;gBAChDqB,KAAKE,GAAG,CAACvB,QAAQ;gBACjBa,SAASG,OAAO,CAACR,MAAM,CAACd,OAAOC,QAAQe;gBACvC,OAAO,IAAIzB,mBAAS,CAClBoC,KAAKG,KAAK,CAACZ,OAAO,GAAGA,OAAO,IAAI,IAChCD,iBAAiBjB,QAAQC,QACzBgB,iBAAiBhB,SAASD;YAE9B;QACF;QACAY,gBAAgB;YACdG,cAAc;QAChB;IACF;AACF;AAEO,MAAMjD,SAAS;IACpBiE,SAAS;QACPtC,MAAM;QACNuC,WAAW;QACXC,WAAW;YAAC;SAAgB;QAC5BC,KAAK,IACHC,IAAAA,0CAAyB,EAACC,yBAAM,EAAyBjE;QAC3DkE,KAAK,IACHF,IAAAA,0CAAyB,EACvBG,yBAAM,EACNvE;QAEJwE,uBAAuB;YACrBC,SAAS;YACTC,UAAU;YACVC,YAAY;YACZC,aAAa;YACbC,iBAAiB;YACjBC,WAAW;YACXC,aAAa,EAAE,OAAO;YACtBC,aAAa;YACbC,mBAAmB;YACnBC,kBAAkB;YAClBC,mBAAmB;YACnBC,eAAe;YACfC,gBAAgB;YAChBC,kBAAkB;YAClBC,yBAAyB;YACzBC,gBAAgB;QAClB;QACAC,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;IACAC,MAAM;QACJnE,MAAM;QACNuC,WAAW;QACXC,WAAW;YAAC;SAAyB;QACrCC,KAAK,IACHC,IAAAA,0CAAyB,EAAC0B,sBAAO,EAAyBxF;QAC5DgE,KAAK,IACHF,IAAAA,0CAAyB,EACvB2B,sBAAO,EACP1F;QAEJmE,uBAAuB;YACrBC,SAAS;YACTuB,aAAa;YACbC,aAAa;YACbzD,QAAQ;YACR0D,cAAc;YACdC,iBAAiB;YACjBC,kBAAkB;YAClBC,aAAa;YACbC,YAAY;YACZC,UAAU;YACVC,MAAM;YACNC,iBAAiB;YACjBC,eAAe;YACfC,YAAY;YACZC,iBAAiB;YACjBC,mBAAmB;YACnBC,iBAAiB;YACjBC,eAAe;YACfC,UAAU;YACVC,OAAO;YACPC,YAAY;YACZC,mBAAmB;YACnBC,cAAc;YACdC,YAAY;YACZC,eAAe;YACfC,mBAAmB;YACnBC,eAAe;QACjB;QACA/B,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;IACA6B,MAAM;QACJ/F,MAAM;QACNuC,WAAW;QACX,4CAA4C;QAC5CC,WAAW;YAAC;SAAyC;QACrDC,KAAK,IACHC,IAAAA,0CAAyB,EAACsD,sBAAO,EAAyBlH;QAC5D8D,KAAK;YACH,OAAOF,IAAAA,0CAAyB,EAC9BuD,sBAAO,EACPpH;QAEJ;QACAiE,uBAAuB;YACrBoD,SAAS;YACTC,cAAc,CAAC;YACfC,cAAc;YACdC,cAAc;YACdC,cAAc;YACdC,OAAO;YACPC,WAAW;YACXC,cAAc;YACdC,WAAW;YACXC,MAAM,EAAE,iBAAiB;QAC3B;QACA5C,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;IACA1E,QAAQ;QACNQ,MAAM;QACNuC,WAAW;QACX,4CAA4C;QAC5CC,WAAW;YAAC;SAA2B;QACvCC,KAAK;YACH,MAAMzD;YACN,OAAO;gBACL4H,QAAQ,CAAC/F;oBACP,MAAMI,YAAYhC,aAAU2H,MAAM,CAAC/F;oBACnC5B,aAAUiC,OAAO;oBACjB,OAAOD;gBACT;YACF;QACF;QACA2B,KAAK;YACH,MAAM5D;YACN,MAAMO;YACN,OAAO;gBACLsH,QAAQ,CACNhG,QACAN,OACAC,QACAsG;oBAEA,MAAMC,YAAY9H,aAAU4H,MAAM,CAChC,IAAIG,WAAWnG,SACfN,OACAC;oBAEF,MAAMS,YAAYzB,gBAAOyH,QAAQ,CAACF,WAAWD,KAAKI,KAAK,EAAE;oBACzD1H,gBAAO0B,OAAO;oBACd,OAAOD;gBACT;YACF;QACF;QACA6B,uBAAuB;YACrBoE,OAAO;QACT;QACAnD,cAAc;YACZC,QAAQ;YACRC,KAAK;YACLC,KAAK;QACP;IACF;AACF"}