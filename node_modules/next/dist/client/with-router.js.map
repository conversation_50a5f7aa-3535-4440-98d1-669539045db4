{"version": 3, "sources": ["../../src/client/with-router.tsx"], "names": ["with<PERSON><PERSON><PERSON>", "ComposedComponent", "WithRouterWrapper", "props", "router", "useRouter", "getInitialProps", "origGetInitialProps", "process", "env", "NODE_ENV", "name", "displayName"], "mappings": ";;;;+BAkBA;;;eAAwBA;;;;gEAlBN;wBAOQ;AAWX,SAASA,WAItBC,iBAA+C;IAE/C,SAASC,kBAAkBC,KAAU;QACnC,qBAAO,6BAACF;YAAkBG,QAAQC,IAAAA,iBAAS;YAAK,GAAGF,KAAK;;IAC1D;IAEAD,kBAAkBI,eAAe,GAAGL,kBAAkBK,eAAe;IAEnEJ,kBAA0BK,mBAAmB,GAAG,AAChDN,kBACAM,mBAAmB;IACrB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,OACJV,kBAAkBW,WAAW,IAAIX,kBAAkBU,IAAI,IAAI;QAC7DT,kBAAkBU,WAAW,GAAG,AAAC,gBAAaD,OAAK;IACrD;IAEA,OAAOT;AACT"}