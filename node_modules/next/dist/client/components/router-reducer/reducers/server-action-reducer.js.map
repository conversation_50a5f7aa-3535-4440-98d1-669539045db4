{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/server-action-reducer.ts"], "names": ["serverActionReducer", "createFromFetch", "encodeReply", "process", "env", "NEXT_RUNTIME", "require", "fetchServerAction", "state", "actionId", "actionArgs", "body", "newNextUrl", "extractPathFromFlightRouterState", "tree", "includeNextUrl", "nextUrl", "res", "fetch", "method", "headers", "Accept", "RSC_CONTENT_TYPE_HEADER", "ACTION", "NEXT_ROUTER_STATE_TREE", "encodeURIComponent", "JSON", "stringify", "__NEXT_ACTIONS_DEPLOYMENT_ID", "NEXT_DEPLOYMENT_ID", "NEXT_URL", "location", "get", "revalidatedParts", "revalidatedHeader", "parse", "paths", "tag", "cookie", "e", "redirectLocation", "URL", "addBasePath", "canonicalUrl", "window", "href", "undefined", "isFlightResponse", "response", "Promise", "resolve", "callServer", "actionFlightData", "actionResult", "action", "reject", "mutable", "currentTree", "preserveCustomHistoryState", "inFlightServerAction", "then", "flightData", "pushRef", "pendingPush", "actionResultResolved", "handleExternalUrl", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "applyRouterStatePatchToTree", "Error", "isNavigatingToNewRootLayout", "cacheNodeSeedData", "head", "slice", "subTreeData", "cache", "createEmptyCacheNode", "status", "CacheStates", "READY", "fillLazyItemsTillLeafWithHead", "prefetchCache", "Map", "patchedTree", "newHref", "createHrefFromUrl", "handleMutable", "reason"], "mappings": ";;;;+BA4JgBA;;;eAAAA;;;+BAvJW;kCAMpB;6BAmBqB;mCACM;iCACA;6CACU;6CACA;+CAIrC;+BACuB;+CACgB;2BACT;oCACY;AA9BjD,gEAAgE;AAChE,oEAAoE;AACpE,gEAAgE;AAChE,gEAAgE;AAChE,MAAM,EAAEC,eAAe,EAAEC,WAAW,EAAE,GACpC,CAAC,CAACC,QAAQC,GAAG,CAACC,YAAY,GAEtBC,QAAQ,0CAERA,QAAQ;AAkCd,eAAeC,kBACbC,KAA2B,EAC3B,KAA4C;IAA5C,IAAA,EAAEC,QAAQ,EAAEC,UAAU,EAAsB,GAA5C;IAEA,MAAMC,OAAO,MAAMT,YAAYQ;IAE/B,MAAME,aAAaC,IAAAA,oDAAgC,EAACL,MAAMM,IAAI;IAC9D,sFAAsF;IACtF,uGAAuG;IACvG,wFAAwF;IACxF,iFAAiF;IACjF,MAAMC,iBAAiBP,MAAMQ,OAAO,IAAIR,MAAMQ,OAAO,KAAKJ;IAE1D,MAAMK,MAAM,MAAMC,MAAM,IAAI;QAC1BC,QAAQ;QACRC,SAAS;YACPC,QAAQC,yCAAuB;YAC/B,CAACC,wBAAM,CAAC,EAAEd;YACV,CAACe,wCAAsB,CAAC,EAAEC,mBAAmBC,KAAKC,SAAS,CAACnB,MAAMM,IAAI;YACtE,GAAIX,QAAQC,GAAG,CAACwB,4BAA4B,IAC5CzB,QAAQC,GAAG,CAACyB,kBAAkB,GAC1B;gBACE,mBAAmB1B,QAAQC,GAAG,CAACyB,kBAAkB;YACnD,IACA,CAAC,CAAC;YACN,GAAId,iBACA;gBACE,CAACe,0BAAQ,CAAC,EAAEtB,MAAMQ,OAAO;YAC3B,IACA,CAAC,CAAC;QACR;QACAL;IACF;IAEA,MAAMoB,WAAWd,IAAIG,OAAO,CAACY,GAAG,CAAC;IACjC,IAAIC;IACJ,IAAI;QACF,MAAMC,oBAAoBR,KAAKS,KAAK,CAClClB,IAAIG,OAAO,CAACY,GAAG,CAAC,2BAA2B;QAE7CC,mBAAmB;YACjBG,OAAOF,iBAAiB,CAAC,EAAE,IAAI,EAAE;YACjCG,KAAK,CAAC,CAACH,iBAAiB,CAAC,EAAE;YAC3BI,QAAQJ,iBAAiB,CAAC,EAAE;QAC9B;IACF,EAAE,OAAOK,GAAG;QACVN,mBAAmB;YACjBG,OAAO,EAAE;YACTC,KAAK;YACLC,QAAQ;QACV;IACF;IAEA,MAAME,mBAAmBT,WACrB,IAAIU,IACFC,IAAAA,wBAAW,EAACX,WACZ,sFAAsF;IACtF,IAAIU,IAAIjC,MAAMmC,YAAY,EAAEC,OAAOb,QAAQ,CAACc,IAAI,KAElDC;IAEJ,IAAIC,mBACF9B,IAAIG,OAAO,CAACY,GAAG,CAAC,oBAAoBV,yCAAuB;IAE7D,IAAIyB,kBAAkB;QACpB,MAAMC,WAAiC,MAAM/C,gBAC3CgD,QAAQC,OAAO,CAACjC,MAChB;YACEkC,YAAAA,yBAAU;QACZ;QAGF,IAAIpB,UAAU;YACZ,qEAAqE;YACrE,MAAM,GAAGqB,iBAAiB,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;YACpD,OAAO;gBACLI,kBAAkBA;gBAClBZ;gBACAP;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,CAACoB,cAAc,GAAGD,iBAAiB,CAAC,GAAG,AAACJ,mBAAAA,WAAoB,EAAE;QACpE,OAAO;YACLK;YACAD;YACAZ;YACAP;QACF;IACF;IACA,OAAO;QACLO;QACAP;IACF;AACF;AAMO,SAASjC,oBACdQ,KAA2B,EAC3B8C,MAA0B;IAE1B,MAAM,EAAEJ,OAAO,EAAEK,MAAM,EAAE,GAAGD;IAC5B,MAAME,UAA+B,CAAC;IACtC,MAAMX,OAAOrC,MAAMmC,YAAY;IAE/B,IAAIc,cAAcjD,MAAMM,IAAI;IAE5B0C,QAAQE,0BAA0B,GAAG;IACrCF,QAAQG,oBAAoB,GAAGpD,kBAAkBC,OAAO8C;IAExD,gDAAgD;IAEhD,OAAOE,QAAQG,oBAAoB,CAACC,IAAI,CACtC;YAAC,EAAEP,YAAY,EAAED,kBAAkBS,UAAU,EAAErB,gBAAgB,EAAE;QAC/D,4DAA4D;QAC5D,wDAAwD;QACxD,IAAIA,kBAAkB;YACpBhC,MAAMsD,OAAO,CAACC,WAAW,GAAG;YAC5BP,QAAQO,WAAW,GAAG;QACxB;QAEA,IAAI,CAACF,YAAY;YACf,IAAI,CAACL,QAAQQ,oBAAoB,EAAE;gBACjCd,QAAQG;gBACRG,QAAQQ,oBAAoB,GAAG;YACjC;YAEA,2EAA2E;YAC3E,IAAIxB,kBAAkB;gBACpB,OAAOyB,IAAAA,kCAAiB,EACtBzD,OACAgD,SACAhB,iBAAiBK,IAAI,EACrBrC,MAAMsD,OAAO,CAACC,WAAW;YAE7B;YACA,OAAOvD;QACT;QAEA,IAAI,OAAOqD,eAAe,UAAU;YAClC,4DAA4D;YAC5D,OAAOI,IAAAA,kCAAiB,EACtBzD,OACAgD,SACAK,YACArD,MAAMsD,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DP,QAAQG,oBAAoB,GAAG;QAE/B,KAAK,MAAMO,kBAAkBL,WAAY;YACvC,oFAAoF;YACpF,IAAIK,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAO7D;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAAC8D,UAAU,GAAGJ;YACpB,MAAMK,UAAUC,IAAAA,wDAA2B,EACzC,sBAAsB;YACtB;gBAAC;aAAG,EACJf,aACAa;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIE,MAAM;YAClB;YAEA,IAAIC,IAAAA,wDAA2B,EAACjB,aAAac,UAAU;gBACrD,OAAON,IAAAA,kCAAiB,EACtBzD,OACAgD,SACAX,MACArC,MAAMsD,OAAO,CAACC,WAAW;YAE7B;YAEA,0DAA0D;YAC1D,MAAM,CAACY,mBAAmBC,KAAK,GAAGV,eAAeW,KAAK,CAAC,CAAC;YACxD,MAAMC,cACJH,sBAAsB,OAAOA,iBAAiB,CAAC,EAAE,GAAG;YAEtD,8FAA8F;YAC9F,IAAIG,gBAAgB,MAAM;gBACxB,MAAMC,QAAmBC,IAAAA,+BAAoB;gBAC7CD,MAAME,MAAM,GAAGC,0CAAW,CAACC,KAAK;gBAChCJ,MAAMD,WAAW,GAAGA;gBACpBM,IAAAA,4DAA6B,EAC3BL,OACA,4FAA4F;gBAC5FjC,WACAwB,WACAK,mBACAC;gBAEFpB,QAAQuB,KAAK,GAAGA;gBAChBvB,QAAQ6B,aAAa,GAAG,IAAIC;YAC9B;YAEA9B,QAAQ+B,WAAW,GAAGhB;YACtBf,QAAQb,YAAY,GAAGE;YAEvBY,cAAcc;QAChB;QAEA,IAAI/B,kBAAkB;YACpB,MAAMgD,UAAUC,IAAAA,oCAAiB,EAACjD,kBAAkB;YACpDgB,QAAQb,YAAY,GAAG6C;QACzB;QAEA,IAAI,CAAChC,QAAQQ,oBAAoB,EAAE;YACjCd,QAAQG;YACRG,QAAQQ,oBAAoB,GAAG;QACjC;QACA,OAAO0B,IAAAA,4BAAa,EAAClF,OAAOgD;IAC9B,GACA,CAACjB;QACC,IAAIA,EAAE0C,MAAM,KAAK,YAAY;YAC3B,IAAI,CAACzB,QAAQQ,oBAAoB,EAAE;gBACjCT,OAAOhB,EAAEoD,MAAM;gBACfnC,QAAQQ,oBAAoB,GAAG;YACjC;YAEA,mHAAmH;YACnH,OAAOxD;QACT;QAEA,MAAM+B;IACR;AAEJ"}