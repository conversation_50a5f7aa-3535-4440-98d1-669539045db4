{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/container/RootLayoutError.tsx"], "names": ["RootLayoutError", "styles", "BuildError", "missingTags", "message", "length", "join", "noop", "React", "useCallback", "Overlay", "fixed", "Dialog", "type", "aria-<PERSON>by", "aria-<PERSON><PERSON>", "onClose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogHeader", "className", "h4", "id", "DialogBody", "Terminal", "content", "footer", "p", "small", "css"], "mappings": ";;;;;;;;;;;;;;;IAaaA,eAAe;eAAfA;;IAyCAC,MAAM;eAANA;;;;;gEAtDK;wBAMX;yBACiB;0BACC;8BACG;;;;;;;;;;AAIrB,MAAMD,kBACX,SAASE,WAAW,KAAe;IAAf,IAAA,EAAEC,WAAW,EAAE,GAAf;IAClB,MAAMC,UACJ,4FACA,CAAA,AAAC,qCACCD,CAAAA,YAAYE,MAAM,KAAK,IAAI,KAAK,GAAE,IACnC,IAAE,IACHF,YAAYG,IAAI,CAAC;IAEnB,MAAMC,OAAOC,cAAK,CAACC,WAAW,CAAC,KAAO,GAAG,EAAE;IAC3C,qBACE,6BAACC,gBAAO;QAACC,OAAAA;qBACP,6BAACC,cAAM;QACLC,MAAK;QACLC,mBAAgB;QAChBC,oBAAiB;QACjBC,SAAST;qBAET,6BAACU,qBAAa,sBACZ,6BAACC,oBAAY;QAACC,WAAU;qBACtB,6BAACC;QAAGC,IAAG;OAA4C,yCAIrD,6BAACC,kBAAU;QAACH,WAAU;qBACpB,6BAACI,kBAAQ;QAACC,SAASpB;sBACnB,6BAACqB,8BACC,6BAACC;QAAEL,IAAG;qBACJ,6BAACM,eAAM;AAWvB;AAEK,MAAM1B,aAAS2B,kBAAG"}