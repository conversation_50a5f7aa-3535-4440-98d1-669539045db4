{"version": 3, "sources": ["../../../src/server/async-storage/request-async-storage-wrapper.ts"], "names": ["FLIGHT_PARAMETERS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MutableRequestCookiesAdapter", "RequestCookiesAdapter", "RequestCookies", "DraftModeProvider", "getHeaders", "headers", "cleaned", "from", "param", "delete", "toString", "toLowerCase", "seal", "getCookies", "cookies", "getMutableCookies", "onUpdateCookies", "wrap", "RequestAsyncStorageWrapper", "storage", "req", "res", "renderOpts", "callback", "previewProps", "undefined", "defaultOnUpdateCookies", "<PERSON><PERSON><PERSON><PERSON>", "cache", "store", "mutableCookies", "draftMode", "run"], "mappings": "AASA,SAASA,iBAAiB,QAAQ,6CAA4C;AAC9E,SACEC,cAAc,QAET,yCAAwC;AAC/C,SACEC,4BAA4B,EAC5BC,qBAAqB,QAEhB,iDAAgD;AAEvD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,iBAAiB,QAAQ,wBAAuB;AAEzD,SAASC,WAAWC,OAAsC;IACxD,MAAMC,UAAUP,eAAeQ,IAAI,CAACF;IACpC,KAAK,MAAMG,SAASV,kBAAmB;QACrCQ,QAAQG,MAAM,CAACD,MAAME,QAAQ,GAAGC,WAAW;IAC7C;IAEA,OAAOZ,eAAea,IAAI,CAACN;AAC7B;AAEA,SAASO,WACPR,OAAsC;IAEtC,MAAMS,UAAU,IAAIZ,eAAeH,eAAeQ,IAAI,CAACF;IACvD,OAAOJ,sBAAsBW,IAAI,CAACE;AACpC;AAEA,SAASC,kBACPV,OAAsC,EACtCW,eAA6C;IAE7C,MAAMF,UAAU,IAAIZ,eAAeH,eAAeQ,IAAI,CAACF;IACvD,OAAOL,6BAA6BiB,IAAI,CAACH,SAASE;AACpD;AAQA,OAAO,MAAME,6BAGT;IACF;;;;;;;;GAQC,GACDD,MACEE,OAAwC,EACxC,EAAEC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAkB,EACxCC,QAAyC;QAEzC,IAAIC,eAA8CC;QAElD,IAAIH,cAAc,kBAAkBA,YAAY;YAC9C,yDAAyD;YACzDE,eAAe,AAACF,WAAmBE,YAAY;QACjD;QAEA,SAASE,uBAAuBZ,OAAiB;YAC/C,IAAIO,KAAK;gBACPA,IAAIM,SAAS,CAAC,cAAcb;YAC9B;QACF;QAEA,MAAMc,QAKF,CAAC;QAEL,MAAMC,QAAsB;YAC1B,IAAIxB,WAAU;gBACZ,IAAI,CAACuB,MAAMvB,OAAO,EAAE;oBAClB,oEAAoE;oBACpE,8BAA8B;oBAC9BuB,MAAMvB,OAAO,GAAGD,WAAWgB,IAAIf,OAAO;gBACxC;gBAEA,OAAOuB,MAAMvB,OAAO;YACtB;YACA,IAAIS,WAAU;gBACZ,IAAI,CAACc,MAAMd,OAAO,EAAE;oBAClB,oEAAoE;oBACpE,8BAA8B;oBAC9Bc,MAAMd,OAAO,GAAGD,WAAWO,IAAIf,OAAO;gBACxC;gBAEA,OAAOuB,MAAMd,OAAO;YACtB;YACA,IAAIgB,kBAAiB;gBACnB,IAAI,CAACF,MAAME,cAAc,EAAE;oBACzBF,MAAME,cAAc,GAAGf,kBACrBK,IAAIf,OAAO,EACXiB,CAAAA,8BAAAA,WAAYN,eAAe,KACxBK,CAAAA,MAAMK,yBAAyBD,SAAQ;gBAE9C;gBACA,OAAOG,MAAME,cAAc;YAC7B;YACA,IAAIC,aAAY;gBACd,IAAI,CAACH,MAAMG,SAAS,EAAE;oBACpBH,MAAMG,SAAS,GAAG,IAAI5B,kBACpBqB,cACAJ,KACA,IAAI,CAACN,OAAO,EACZ,IAAI,CAACgB,cAAc;gBAEvB;gBAEA,OAAOF,MAAMG,SAAS;YACxB;QACF;QAEA,OAAOZ,QAAQa,GAAG,CAACH,OAAON,UAAUM;IACtC;AACF,EAAC"}