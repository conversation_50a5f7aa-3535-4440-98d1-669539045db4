{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-no-store.ts"], "names": ["staticGenerationAsyncStorage", "staticGenerationBailout", "unstable_noStore", "staticGenerationStore", "getStore", "isUnstableCacheCallback", "link"], "mappings": "AAAA,SAASA,4BAA4B,QAAQ,sEAAqE;AAClH,SAASC,uBAAuB,QAAQ,uDAAsD;AAE9F,OAAO,SAASC;IACd,MAAMC,wBAAwBH,6BAA6BI,QAAQ;IAEnE,IAAID,yCAAAA,sBAAuBE,uBAAuB,EAAE;QAClD,kEAAkE;QAClE,sEAAsE;QACtE;IACF;IAEAJ,wBAAwB,oBAAoB;QAC1CK,MAAM;IACR;AACF"}