{"version": 3, "sources": ["../../../../src/server/web/spec-extension/request.ts"], "names": ["NextURL", "toNodeOutgoingHttpHeaders", "validateURL", "RemovedUAError", "RemovedPageError", "RequestCookies", "INTERNALS", "Symbol", "NextRequest", "Request", "constructor", "input", "init", "url", "String", "nextUrl", "headers", "nextConfig", "cookies", "geo", "ip", "process", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "toString", "for", "bodyUsed", "cache", "credentials", "destination", "Object", "fromEntries", "integrity", "keepalive", "method", "mode", "redirect", "referrer", "referrerPolicy", "signal", "page", "ua"], "mappings": "AAEA,SAASA,OAAO,QAAQ,cAAa;AACrC,SAASC,yBAAyB,EAAEC,WAAW,QAAQ,WAAU;AACjE,SAASC,cAAc,EAAEC,gBAAgB,QAAQ,WAAU;AAC3D,SAASC,cAAc,QAAQ,YAAW;AAE1C,OAAO,MAAMC,YAAYC,OAAO,oBAAmB;AAEnD,OAAO,MAAMC,oBAAoBC;IAS/BC,YAAYC,KAAwB,EAAEC,OAAoB,CAAC,CAAC,CAAE;QAC5D,MAAMC,MACJ,OAAOF,UAAU,YAAY,SAASA,QAAQA,MAAME,GAAG,GAAGC,OAAOH;QACnET,YAAYW;QACZ,IAAIF,iBAAiBF,SAAS,KAAK,CAACE,OAAOC;aACtC,KAAK,CAACC,KAAKD;QAChB,MAAMG,UAAU,IAAIf,QAAQa,KAAK;YAC/BG,SAASf,0BAA0B,IAAI,CAACe,OAAO;YAC/CC,YAAYL,KAAKK,UAAU;QAC7B;QACA,IAAI,CAACX,UAAU,GAAG;YAChBY,SAAS,IAAIb,eAAe,IAAI,CAACW,OAAO;YACxCG,KAAKP,KAAKO,GAAG,IAAI,CAAC;YAClBC,IAAIR,KAAKQ,EAAE;YACXL;YACAF,KAAKQ,QAAQC,GAAG,CAACC,kCAAkC,GAC/CV,MACAE,QAAQS,QAAQ;QACtB;IACF;IAEA,CAACjB,OAAOkB,GAAG,CAAC,+BAA+B,GAAG;QAC5C,OAAO;YACLP,SAAS,IAAI,CAACA,OAAO;YACrBC,KAAK,IAAI,CAACA,GAAG;YACbC,IAAI,IAAI,CAACA,EAAE;YACXL,SAAS,IAAI,CAACA,OAAO;YACrBF,KAAK,IAAI,CAACA,GAAG;YACb,kCAAkC;YAClCa,UAAU,IAAI,CAACA,QAAQ;YACvBC,OAAO,IAAI,CAACA,KAAK;YACjBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,aAAa,IAAI,CAACA,WAAW;YAC7Bb,SAASc,OAAOC,WAAW,CAAC,IAAI,CAACf,OAAO;YACxCgB,WAAW,IAAI,CAACA,SAAS;YACzBC,WAAW,IAAI,CAACA,SAAS;YACzBC,QAAQ,IAAI,CAACA,MAAM;YACnBC,MAAM,IAAI,CAACA,IAAI;YACfC,UAAU,IAAI,CAACA,QAAQ;YACvBC,UAAU,IAAI,CAACA,QAAQ;YACvBC,gBAAgB,IAAI,CAACA,cAAc;YACnCC,QAAQ,IAAI,CAACA,MAAM;QACrB;IACF;IAEA,IAAWrB,UAAU;QACnB,OAAO,IAAI,CAACZ,UAAU,CAACY,OAAO;IAChC;IAEA,IAAWC,MAAM;QACf,OAAO,IAAI,CAACb,UAAU,CAACa,GAAG;IAC5B;IAEA,IAAWC,KAAK;QACd,OAAO,IAAI,CAACd,UAAU,CAACc,EAAE;IAC3B;IAEA,IAAWL,UAAU;QACnB,OAAO,IAAI,CAACT,UAAU,CAACS,OAAO;IAChC;IAEA;;;;GAIC,GACD,IAAWyB,OAAO;QAChB,MAAM,IAAIpC;IACZ;IAEA;;;;GAIC,GACD,IAAWqC,KAAK;QACd,MAAM,IAAItC;IACZ;IAEA,IAAWU,MAAM;QACf,OAAO,IAAI,CAACP,UAAU,CAACO,GAAG;IAC5B;AACF"}