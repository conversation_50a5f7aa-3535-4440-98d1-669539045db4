{"version": 3, "sources": ["../../../src/server/lib/start-server.ts"], "names": ["performance", "getEntriesByName", "length", "mark", "fs", "v8", "path", "http", "https", "os", "Watchpack", "Log", "setupDebug", "RESTART_EXIT_CODE", "checkNodeDebugType", "getDebugPort", "formatHostname", "initialize", "CONFIG_FILES", "getStartServerInfo", "logStartInfo", "validateTurboNextConfig", "trace", "flushAllTraces", "isPostpone", "debug", "startServerSpan", "getRequestHandlers", "dir", "port", "isDev", "server", "hostname", "minimalMode", "isNodeDebugging", "keepAliveTimeout", "experimentalTestProxy", "experimentalHttpsServer", "dev", "startServer", "serverOptions", "allowRetry", "isExperimentalTestProxy", "selfSignedCertificate", "process", "title", "handlersReady", "handlersError", "handlersPromise", "Promise", "resolve", "reject", "requestHandler", "req", "res", "Error", "upgradeHandler", "socket", "head", "requestListener", "undefined", "err", "statusCode", "end", "error", "url", "console", "getHeapStatistics", "used_heap_size", "heap_size_limit", "warn", "String", "stop", "exit", "createServer", "key", "readFileSync", "cert", "on", "destroy", "portRetryCount", "code", "listen", "nodeDebugType", "addr", "address", "actualHostname", "formattedHostname", "networkUrl", "appUrl", "debugPort", "info", "env", "PORT", "envInfo", "expFeatureInfo", "startServerInfo", "maxExperimentalFeatures", "cleanup", "close", "exception", "NEXT_MANUAL_SIG_HANDLE", "initResult", "Boolean", "startServerProcessDuration", "measure", "duration", "formatDurationText", "Math", "round", "event", "TURBOPACK", "watchConfigFiles", "dirToWatch", "onChange", "wp", "watch", "files", "map", "file", "join", "filename", "__NEXT_DISABLE_MEMORY_WATCHER", "basename", "NEXT_PRIVATE_WORKER", "send", "addListener", "msg", "nextWorkerOptions", "cpus", "platform", "freemem", "totalmem", "traceAsyncFn", "memoryUsage", "setAttribute", "rss", "heapTotal", "heapUsed", "nextServerReady", "nextWorkerReady"], "mappings": "AAAA,IAAIA,YAAYC,gBAAgB,CAAC,cAAcC,MAAM,KAAK,GAAG;IAC3DF,YAAYG,IAAI,CAAC;AACnB;AACA,OAAO,UAAS;AAChB,OAAO,kBAAiB;AAMxB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,UAAU,OAAM;AACvB,OAAOC,UAAU,OAAM;AACvB,OAAOC,WAAW,QAAO;AACzB,OAAOC,QAAQ,KAAI;AACnB,OAAOC,eAAe,YAAW;AACjC,YAAYC,SAAS,yBAAwB;AAC7C,OAAOC,gBAAgB,2BAA0B;AACjD,SAASC,iBAAiB,EAAEC,kBAAkB,EAAEC,YAAY,QAAQ,UAAS;AAC7E,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,UAAU,QAAQ,kBAAiB;AAC5C,SAASC,YAAY,QAAQ,6BAA4B;AACzD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,iBAAgB;AACjE,SAASC,uBAAuB,QAAQ,8BAA6B;AACrE,SAAoBC,KAAK,EAAEC,cAAc,QAAQ,cAAa;AAC9D,SAASC,UAAU,QAAQ,6BAA4B;AAEvD,MAAMC,QAAQb,WAAW;AACzB,IAAIc;AAgBJ,OAAO,eAAeC,mBAAmB,EACvCC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,WAAW,EACXC,eAAe,EACfC,gBAAgB,EAChBC,qBAAqB,EACrBC,uBAAuB,EAYxB;IACC,OAAOpB,WAAW;QAChBW;QACAC;QACAG;QACAM,KAAKR;QACLG;QACAF;QACAG,iBAAiBA,mBAAmB;QACpCC;QACAC;QACAC;QACAX;IACF;AACF;AAEA,OAAO,eAAea,YACpBC,aAAiC;IAEjC,MAAM,EACJZ,GAAG,EACHE,KAAK,EACLE,QAAQ,EACRC,WAAW,EACXQ,UAAU,EACVN,gBAAgB,EAChBO,uBAAuB,EACvBC,qBAAqB,EACtB,GAAGH;IACJ,IAAI,EAAEX,IAAI,EAAE,GAAGW;IAEfI,QAAQC,KAAK,GAAG;IAChB,IAAIC,gBAAgB,KAAO;IAC3B,IAAIC,gBAAgB,KAAO;IAE3B,IAAIC,kBAA6C,IAAIC,QACnD,CAACC,SAASC;QACRL,gBAAgBI;QAChBH,gBAAgBI;IAClB;IAEF,IAAIC,iBAAuC,OACzCC,KACAC;QAEA,IAAIN,iBAAiB;YACnB,MAAMA;YACN,OAAOI,eAAeC,KAAKC;QAC7B;QACA,MAAM,IAAIC,MAAM;IAClB;IACA,IAAIC,iBAAuC,OACzCH,KACAI,QACAC;QAEA,IAAIV,iBAAiB;YACnB,MAAMA;YACN,OAAOQ,eAAeH,KAAKI,QAAQC;QACrC;QACA,MAAM,IAAIH,MAAM;IAClB;IAEA,4CAA4C;IAC5C,IAAIZ,yBAAyB,CAACb,OAAO;QACnC,MAAM,IAAIyB,MACR;IAEJ;IAEA,eAAeI,gBAAgBN,GAAoB,EAAEC,GAAmB;QACtE,IAAI;YACF,IAAIN,iBAAiB;gBACnB,MAAMA;gBACNA,kBAAkBY;YACpB;YACA,MAAMR,eAAeC,KAAKC;QAC5B,EAAE,OAAOO,KAAK;YACZP,IAAIQ,UAAU,GAAG;YACjBR,IAAIS,GAAG,CAAC;YACRpD,IAAIqD,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB,SAAU;YACR,IAAI/B,OAAO;gBACT,IACEzB,GAAG8D,iBAAiB,GAAGC,cAAc,GACrC,MAAM/D,GAAG8D,iBAAiB,GAAGE,eAAe,EAC5C;oBACA1D,IAAI2D,IAAI,CACN,CAAC,8DAA8D,CAAC;oBAElEhD,MAAM,4CAA4CsC,WAAW;wBAC3D,wBAAwBW,OACtBlE,GAAG8D,iBAAiB,GAAGE,eAAe;wBAExC,mBAAmBE,OAAOlE,GAAG8D,iBAAiB,GAAGC,cAAc;oBACjE,GAAGI,IAAI;oBACP,MAAMjD;oBACNqB,QAAQ6B,IAAI,CAAC5D;gBACf;YACF;QACF;IACF;IAEA,MAAMkB,SAASY,wBACXnC,MAAMkE,YAAY,CAChB;QACEC,KAAKvE,GAAGwE,YAAY,CAACjC,sBAAsBgC,GAAG;QAC9CE,MAAMzE,GAAGwE,YAAY,CAACjC,sBAAsBkC,IAAI;IAClD,GACAlB,mBAEFpD,KAAKmE,YAAY,CAACf;IAEtB,IAAIxB,kBAAkB;QACpBJ,OAAOI,gBAAgB,GAAGA;IAC5B;IACAJ,OAAO+C,EAAE,CAAC,WAAW,OAAOzB,KAAKI,QAAQC;QACvC,IAAI;YACF,MAAMF,eAAeH,KAAKI,QAAQC;QACpC,EAAE,OAAOG,KAAK;YACZJ,OAAOsB,OAAO;YACdpE,IAAIqD,KAAK,CAAC,CAAC,6BAA6B,EAAEX,IAAIY,GAAG,CAAC,CAAC;YACnDC,QAAQF,KAAK,CAACH;QAChB;IACF;IAEA,IAAImB,iBAAiB;IAErBjD,OAAO+C,EAAE,CAAC,SAAS,CAACjB;QAClB,IACEpB,cACAZ,QACAC,SACA+B,IAAIoB,IAAI,KAAK,gBACbD,iBAAiB,IACjB;YACArE,IAAI2D,IAAI,CAAC,CAAC,KAAK,EAAEzC,KAAK,mBAAmB,EAAEA,OAAO,EAAE,SAAS,CAAC;YAC9DA,QAAQ;YACRmD,kBAAkB;YAClBjD,OAAOmD,MAAM,CAACrD,MAAMG;QACtB,OAAO;YACLrB,IAAIqD,KAAK,CAAC,CAAC,sBAAsB,CAAC;YAClCE,QAAQF,KAAK,CAACH;YACdjB,QAAQ6B,IAAI,CAAC;QACf;IACF;IAEA,MAAMU,gBAAgBrE;IAEtB,MAAM,IAAImC,QAAc,CAACC;QACvBnB,OAAO+C,EAAE,CAAC,aAAa;YACrB,MAAMM,OAAOrD,OAAOsD,OAAO;YAC3B,MAAMC,iBAAiBtE,eACrB,OAAOoE,SAAS,WACZA,CAAAA,wBAAAA,KAAMC,OAAO,KAAIrD,YAAY,cAC7BoD;YAEN,MAAMG,oBACJ,CAACvD,YAAYsD,mBAAmB,YAC5B,cACAA,mBAAmB,SACnB,UACAtE,eAAegB;YAErBH,OAAO,OAAOuD,SAAS,WAAWA,CAAAA,wBAAAA,KAAMvD,IAAI,KAAIA,OAAOA;YAEvD,MAAM2D,aAAaxD,WAAW,CAAC,OAAO,EAAEsD,eAAe,CAAC,EAAEzD,KAAK,CAAC,GAAG;YACnE,MAAM4D,SAAS,CAAC,EACd9C,wBAAwB,UAAU,OACnC,GAAG,EAAE4C,kBAAkB,CAAC,EAAE1D,KAAK,CAAC;YAEjC,IAAIsD,eAAe;gBACjB,MAAMO,YAAY3E;gBAClBJ,IAAIgF,IAAI,CACN,CAAC,MAAM,EAAER,cAAc,4EAA4E,EAAEO,UAAU,CAAC,CAAC;YAErH;YAEA,yCAAyC;YACzC9C,QAAQgD,GAAG,CAACC,IAAI,GAAGhE,OAAO;YAE1B,0DAA0D;YAC1D,IAAIiE;YACJ,IAAIC;YACJ,IAAIjE,OAAO;gBACT,MAAMkE,kBAAkB,MAAM7E,mBAAmBS;gBACjDkE,UAAUE,gBAAgBF,OAAO;gBACjCC,iBAAiBC,gBAAgBD,cAAc;YACjD;YACA3E,aAAa;gBACXoE;gBACAC;gBACAK;gBACAC;gBACAE,yBAAyB;YAC3B;YAEA,IAAI;gBACF,MAAMC,UAAU,CAACjB;oBACfxD,MAAM;oBACNM,OAAOoE,KAAK;oBACZvD,QAAQ6B,IAAI,CAACQ,QAAQ;gBACvB;gBACA,MAAMmB,YAAY,CAACvC;oBACjB,IAAIrC,WAAWqC,MAAM;wBACnB,0EAA0E;wBAC1E,qDAAqD;wBACrD;oBACF;oBAEA,uDAAuD;oBACvDK,QAAQF,KAAK,CAACH;gBAChB;gBACAjB,QAAQkC,EAAE,CAAC,QAAQ,CAACG,OAASiB,QAAQjB;gBACrC,IAAI,CAACrC,QAAQgD,GAAG,CAACS,sBAAsB,EAAE;oBACvC,+CAA+C;oBAC/CzD,QAAQkC,EAAE,CAAC,UAAU,IAAMoB,QAAQ;oBACnCtD,QAAQkC,EAAE,CAAC,WAAW,IAAMoB,QAAQ;gBACtC;gBACAtD,QAAQkC,EAAE,CAAC,oBAAoB;gBAC7B,sEAAsE;gBACtE,uEAAuE;gBACvE,6DAA6D;gBAC/D;gBACAlC,QAAQkC,EAAE,CAAC,qBAAqBsB;gBAChCxD,QAAQkC,EAAE,CAAC,sBAAsBsB;gBAEjC,MAAME,aAAa,MAAM3E,mBAAmB;oBAC1CC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC,iBAAiBqE,QAAQpB;oBACzBhD;oBACAC,uBAAuB,CAAC,CAACM;oBACzBL,yBAAyB,CAAC,CAACM;gBAC7B;gBACAS,iBAAiBkD,UAAU,CAAC,EAAE;gBAC9B9C,iBAAiB8C,UAAU,CAAC,EAAE;gBAE9B,MAAME,6BACJxG,YAAYG,IAAI,CAAC,qBACjBH,YAAYyG,OAAO,CACjB,uBACA,cACA,kBACAC,QAAQ;gBAEZ5D;gBACA,MAAM6D,qBACJH,6BAA6B,OACzB,CAAC,EAAEI,KAAKC,KAAK,CAACL,6BAA6B,OAAO,GAAG,CAAC,CAAC,GACvD,CAAC,EAAEI,KAAKC,KAAK,CAACL,4BAA4B,EAAE,CAAC;gBAEnD7F,IAAImG,KAAK,CAAC,CAAC,SAAS,EAAEH,mBAAmB,CAAC;gBAE1C,IAAI/D,QAAQgD,GAAG,CAACmB,SAAS,EAAE;oBACzB,MAAM1F,wBAAwB;wBAC5B,GAAGmB,aAAa;wBAChBV,OAAO;oBACT;gBACF;YACF,EAAE,OAAO+B,KAAK;gBACZ,gCAAgC;gBAChCd;gBACAmB,QAAQF,KAAK,CAACH;gBACdjB,QAAQ6B,IAAI,CAAC;YACf;YAEAvB;QACF;QACAnB,OAAOmD,MAAM,CAACrD,MAAMG;IACtB;IAEA,IAAIF,OAAO;QACT,SAASkF,iBACPC,UAAkB,EAClBC,QAAoC;YAEpC,MAAMC,KAAK,IAAIzG;YACfyG,GAAGC,KAAK,CAAC;gBACPC,OAAOnG,aAAaoG,GAAG,CAAC,CAACC,OAASjH,KAAKkH,IAAI,CAACP,YAAYM;YAC1D;YACAJ,GAAGrC,EAAE,CAAC,UAAUoC;QAClB;QACAF,iBAAiBpF,KAAK,OAAO6F;YAC3B,IAAI7E,QAAQgD,GAAG,CAAC8B,6BAA6B,EAAE;gBAC7C/G,IAAIgF,IAAI,CACN,CAAC,qFAAqF,CAAC;gBAEzF;YACF;YAEAhF,IAAI2D,IAAI,CACN,CAAC,kBAAkB,EAAEhE,KAAKqH,QAAQ,CAChCF,UACA,+CAA+C,CAAC;YAEpD7E,QAAQ6B,IAAI,CAAC5D;QACf;IACF;AACF;AAEA,IAAI+B,QAAQgD,GAAG,CAACgC,mBAAmB,IAAIhF,QAAQiF,IAAI,EAAE;IACnDjF,QAAQkF,WAAW,CAAC,WAAW,OAAOC;QACpC,IAAIA,OAAO,OAAOA,OAAOA,IAAIC,iBAAiB,IAAIpF,QAAQiF,IAAI,EAAE;YAC9DnG,kBAAkBJ,MAAM,oBAAoBsC,WAAW;gBACrDqE,MAAM1D,OAAO9D,GAAGwH,IAAI,GAAG/H,MAAM;gBAC7BgI,UAAUzH,GAAGyH,QAAQ;gBACrB,kBAAkB3D,OAAO9D,GAAG0H,OAAO;gBACnC,mBAAmB5D,OAAO9D,GAAG2H,QAAQ;gBACrC,wBAAwB7D,OAAOlE,GAAG8D,iBAAiB,GAAGE,eAAe;YACvE;YACA,MAAM3C,gBAAgB2G,YAAY,CAAC,IACjC9F,YAAYwF,IAAIC,iBAAiB;YAEnC,MAAMM,cAAc1F,QAAQ0F,WAAW;YACvC5G,gBAAgB6G,YAAY,CAAC,cAAchE,OAAO+D,YAAYE,GAAG;YACjE9G,gBAAgB6G,YAAY,CAC1B,oBACAhE,OAAO+D,YAAYG,SAAS;YAE9B/G,gBAAgB6G,YAAY,CAC1B,mBACAhE,OAAO+D,YAAYI,QAAQ;YAE7B9F,QAAQiF,IAAI,CAAC;gBAAEc,iBAAiB;YAAK;QACvC;IACF;IACA/F,QAAQiF,IAAI,CAAC;QAAEe,iBAAiB;IAAK;AACvC"}