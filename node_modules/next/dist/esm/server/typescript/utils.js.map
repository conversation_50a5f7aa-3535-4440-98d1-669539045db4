{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "names": ["path", "ts", "info", "appDirRegExp", "log", "message", "project", "projectService", "logger", "init", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "getTs", "getInfo", "getType<PERSON><PERSON>cker", "languageService", "getProgram", "getSource", "fileName", "getSourceFile", "removeStringQuotes", "str", "isPositionInsideNode", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isDefaultFunctionExport", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "isInsideApp", "filePath", "test", "isAppEntryFile", "basename", "isPageFile", "getIsClientEntry", "throwOnInvalidDirective", "source", "isClientEntry", "isDirective", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AAKvB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEJ,OAAO,SAASC,IAAIC,OAAe;IACjCH,KAAKI,OAAO,CAACC,cAAc,CAACC,MAAM,CAACN,IAAI,CAACG;AAC1C;AAEA,4CAA4C;AAC5C,OAAO,SAASI,KAAKC,IAGpB;IACCT,KAAKS,KAAKT,EAAE;IACZC,OAAOQ,KAAKR,IAAI;IAChB,MAAMS,aAAaT,KAAKI,OAAO,CAACM,mBAAmB;IACnDT,eAAe,IAAIU,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAEvDV,IAAI,yCAAyCO;AAC/C;AAEA,OAAO,SAASI;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;IACd,OAAOd;AACT;AAEA,OAAO,SAASe;QACPf;IAAP,QAAOA,mCAAAA,KAAKgB,eAAe,CAACC,UAAU,uBAA/BjB,iCAAmCe,cAAc;AAC1D;AAEA,OAAO,SAASG,UAAUC,QAAgB;QACjCnB;IAAP,QAAOA,mCAAAA,KAAKgB,eAAe,CAACC,UAAU,uBAA/BjB,iCAAmCoB,aAAa,CAACD;AAC1D;AAEA,OAAO,SAASE,mBAAmBC,GAAW;IAC5C,OAAOA,IAAIV,OAAO,CAAC,kBAAkB;AACvC;AAEA,OAAO,MAAMW,uBAAuB,CAACC,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE,EAAC;AAED,OAAO,MAAMG,0BAA0B,CACrCJ;IAEA,IAAI1B,GAAG+B,qBAAqB,CAACL,OAAO;QAClC,IAAIM,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIP,KAAKQ,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYT,KAAKQ,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAKpC,GAAGqC,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAKpC,GAAGqC,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT,EAAC;AAED,OAAO,MAAMO,cAAc,CAACC;IAC1B,OAAOvC,aAAawC,IAAI,CAACD;AAC3B,EAAC;AACD,OAAO,MAAME,iBAAiB,CAACF;IAC7B,OACEvC,aAAawC,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAAC3C,KAAK6C,QAAQ,CAACH;AAE9D,EAAC;AACD,OAAO,MAAMI,aAAa,CAACJ;IACzB,OACEvC,aAAawC,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAAC3C,KAAK6C,QAAQ,CAACH;AAErD,EAAC;AAED,uCAAuC;AACvC,OAAO,SAASK,iBACd1B,QAAgB,EAChB2B,uBAAiC;IAEjC,MAAMC,SAAS7B,UAAUC;IACzB,IAAI4B,QAAQ;QACV,IAAIC,gBAAgB;QACpB,IAAIC,cAAc;QAElBlD,GAAGmD,YAAY,CAACH,QAAS,CAACtB;YACxB,IACE1B,GAAGoD,qBAAqB,CAAC1B,SACzB1B,GAAGqD,eAAe,CAAC3B,KAAK4B,UAAU,GAClC;gBACA,IAAI5B,KAAK4B,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIL,aAAa;wBACfD,gBAAgB;oBAClB,OAAO;wBACL,IAAIF,yBAAyB;4BAC3B,MAAMS,IAAI;gCACRC,aACE;gCACF9B,OAAOD,KAAK4B,UAAU,CAACI,QAAQ;gCAC/BC,QAAQjC,KAAK4B,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;YACF,OAAO;gBACLN,cAAc;YAChB;QACF;QAEA,OAAOD;IACT;IACA,OAAO;AACT"}