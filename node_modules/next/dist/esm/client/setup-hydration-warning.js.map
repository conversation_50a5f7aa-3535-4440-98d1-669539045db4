{"version": 3, "sources": ["../../src/client/setup-hydration-warning.ts"], "names": ["window", "_nextSetupHydrationWarning", "origConsoleError", "console", "error", "args", "isHydrateError", "some", "arg", "match", "apply"], "mappings": "AAMA,IAAI,CAACA,OAAOC,0BAA0B,EAAE;IACtC,MAAMC,mBAAmBF,OAAOG,OAAO,CAACC,KAAK;IAC7CJ,OAAOG,OAAO,CAACC,KAAK,GAAG;yCAAIC;YAAAA;;QACzB,MAAMC,iBAAiBD,KAAKE,IAAI,CAC9B,CAACC,MACC,OAAOA,QAAQ,YACfA,IAAIC,KAAK,CAAC;QAEd,IAAIH,gBAAgB;YAClBD,OAAO;mBACFA;gBACF;aACF;QACH;QACAH,iBAAiBQ,KAAK,CAACV,OAAOG,OAAO,EAAEE;IACzC;IACAL,OAAOC,0BAA0B,GAAG;AACtC;AAvBA,WAyBS"}