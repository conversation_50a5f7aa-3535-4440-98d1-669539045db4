{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/refresh-reducer.ts"], "names": ["fetchServerResponse", "createHrefFromUrl", "applyRouterStatePatchToTree", "isNavigatingToNewRootLayout", "handleExternalUrl", "handleMutable", "CacheStates", "fillLazyItemsTillLeafWithHead", "createEmptyCacheNode", "refreshReducer", "state", "action", "origin", "mutable", "href", "canonicalUrl", "currentTree", "tree", "preserveCustomHistoryState", "cache", "data", "URL", "nextUrl", "buildId", "then", "flightData", "canonicalUrlOverride", "pushRef", "pendingPush", "flightDataPath", "length", "console", "log", "treePatch", "newTree", "Error", "canonicalUrlOverrideHref", "undefined", "cacheNodeSeedData", "head", "slice", "subTreeData", "status", "READY", "prefetchCache", "Map", "patchedTree"], "mappings": "AAAA,SAASA,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,iBAAiB,QAAQ,0BAAyB;AAC3D,SAASC,2BAA2B,QAAQ,sCAAqC;AACjF,SAASC,2BAA2B,QAAQ,sCAAqC;AAOjF,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SACEC,WAAW,QAEN,2DAA0D;AACjE,SAASC,6BAA6B,QAAQ,yCAAwC;AACtF,SAASC,oBAAoB,QAAQ,mBAAkB;AAEvD,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,MAAM,EAAE,GAAGD;IACnB,MAAME,UAAmB,CAAC;IAC1B,MAAMC,OAAOJ,MAAMK,YAAY;IAE/B,IAAIC,cAAcN,MAAMO,IAAI;IAE5BJ,QAAQK,0BAA0B,GAAG;IAErC,MAAMC,QAAmBX;IACzB,uDAAuD;IACvD,wCAAwC;IACxCW,MAAMC,IAAI,GAAGpB,oBACX,IAAIqB,IAAIP,MAAMF,SACd;QAACI,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAEA,WAAW,CAAC,EAAE;QAAE;KAAU,EAC3DN,MAAMY,OAAO,EACbZ,MAAMa,OAAO;IAGf,OAAOJ,MAAMC,IAAI,CAACI,IAAI,CACpB;YAAC,CAACC,YAAYC,qBAAqB;QACjC,4DAA4D;QAC5D,IAAI,OAAOD,eAAe,UAAU;YAClC,OAAOrB,kBACLM,OACAG,SACAY,YACAf,MAAMiB,OAAO,CAACC,WAAW;QAE7B;QAEA,2DAA2D;QAC3DT,MAAMC,IAAI,GAAG;QAEb,KAAK,MAAMS,kBAAkBJ,WAAY;YACvC,oFAAoF;YACpF,IAAII,eAAeC,MAAM,KAAK,GAAG;gBAC/B,oCAAoC;gBACpCC,QAAQC,GAAG,CAAC;gBACZ,OAAOtB;YACT;YAEA,2GAA2G;YAC3G,MAAM,CAACuB,UAAU,GAAGJ;YACpB,MAAMK,UAAUhC,4BACd,sBAAsB;YACtB;gBAAC;aAAG,EACJc,aACAiB;YAGF,IAAIC,YAAY,MAAM;gBACpB,MAAM,IAAIC,MAAM;YAClB;YAEA,IAAIhC,4BAA4Ba,aAAakB,UAAU;gBACrD,OAAO9B,kBACLM,OACAG,SACAC,MACAJ,MAAMiB,OAAO,CAACC,WAAW;YAE7B;YAEA,MAAMQ,2BAA2BV,uBAC7BzB,kBAAkByB,wBAClBW;YAEJ,IAAIX,sBAAsB;gBACxBb,QAAQE,YAAY,GAAGqB;YACzB;YAEA,0DAA0D;YAC1D,MAAM,CAACE,mBAAmBC,KAAK,GAAGV,eAAeW,KAAK,CAAC,CAAC;YAExD,8FAA8F;YAC9F,IAAIF,sBAAsB,MAAM;gBAC9B,MAAMG,cAAcH,iBAAiB,CAAC,EAAE;gBACxCnB,MAAMuB,MAAM,GAAGpC,YAAYqC,KAAK;gBAChCxB,MAAMsB,WAAW,GAAGA;gBACpBlC,8BACEY,OACA,4FAA4F;gBAC5FkB,WACAJ,WACAK,mBACAC;gBAEF1B,QAAQM,KAAK,GAAGA;gBAChBN,QAAQ+B,aAAa,GAAG,IAAIC;YAC9B;YAEAhC,QAAQiC,WAAW,GAAGZ;YACtBrB,QAAQE,YAAY,GAAGD;YAEvBE,cAAckB;QAChB;QAEA,OAAO7B,cAAcK,OAAOG;IAC9B,GACA,IAAMH;AAEV"}