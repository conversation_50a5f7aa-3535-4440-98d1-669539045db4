{"version": 3, "sources": ["../../../../src/build/webpack/plugins/react-loadable-plugin.ts"], "names": ["webpack", "sources", "path", "getModuleId", "compilation", "module", "chunkGraph", "getModuleFromDependency", "dep", "moduleGraph", "getModule", "getOriginModuleFromDependency", "getParentModule", "getChunkGroupFromBlock", "block", "getBlockChunkGroup", "buildManifest", "_compiler", "pagesDir", "dev", "manifest", "handleBlock", "blocks", "for<PERSON>ach", "chunkGroup", "dependency", "dependencies", "type", "startsWith", "originModule", "originRequest", "resource", "key", "relative", "request", "files", "Set", "file", "add", "chunk", "chunks", "endsWith", "match", "id", "Array", "from", "modules", "Object", "keys", "sort", "reduce", "a", "c", "ReactLoadablePlugin", "constructor", "opts", "filename", "runtimeAsset", "createAssets", "compiler", "assets", "RawSource", "JSON", "stringify", "apply", "hooks", "make", "tap", "processAssets", "name", "stage", "Compilation", "PROCESS_ASSETS_STAGE_ADDITIONS"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;AAmBA,GACA,oFAAoF;AACpF,sEAAsE;AAEtE,SAASA,OAAO,EAAEC,OAAO,QAAQ,qCAAoC;AAErE,OAAOC,UAAU,OAAM;AAEvB,SAASC,YAAYC,WAAgB,EAAEC,MAAW;IAChD,OAAOD,YAAYE,UAAU,CAACH,WAAW,CAACE;AAC5C;AAEA,SAASE,wBACPH,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACC,SAAS,CAACF;AAC3C;AAEA,SAASG,8BACPP,WAAgB,EAChBI,GAAQ;IAER,OAAOJ,YAAYK,WAAW,CAACG,eAAe,CAACJ;AACjD;AAEA,SAASK,uBACPT,WAAgB,EAChBU,KAAU;IAEV,OAAOV,YAAYE,UAAU,CAACS,kBAAkB,CAACD;AACnD;AAEA,SAASE,cACPC,SAA2B,EAC3Bb,WAAgC,EAChCc,QAA4B,EAC5BC,GAAY;IAEZ,mDAAmD;IACnD,IAAI,CAACD,UAAU;QACb,OAAO,CAAC;IACV;IAEA,IAAIE,WAAsE,CAAC;IAE3E,mBAAmB;IACnB,0CAA0C;IAE1C,yBAAyB;IACzB,yEAAyE;IACzE,yDAAyD;IAEzD,sEAAsE;IACtE,MAAMC,cAAc,CAACP;QACnBA,MAAMQ,MAAM,CAACC,OAAO,CAACF;QACrB,MAAMG,aAAaX,uBAAuBT,aAAaU;QACvD,KAAK,MAAMW,cAAcX,MAAMY,YAAY,CAAE;YAC3C,IAAID,WAAWE,IAAI,CAACC,UAAU,CAAC,aAAa;gBAC1C,4BAA4B;gBAC5B,MAAMvB,SAASE,wBAAwBH,aAAaqB;gBACpD,IAAI,CAACpB,QAAQ;gBAEb,yCAAyC;gBACzC,MAAMwB,eAAelB,8BACnBP,aACAqB;gBAEF,MAAMK,gBAAoCD,gCAAAA,aAAcE,QAAQ;gBAChE,IAAI,CAACD,eAAe;gBAEpB,6DAA6D;gBAC7D,yDAAyD;gBACzD,0DAA0D;gBAC1D,MAAME,MAAM,CAAC,EAAE9B,KAAK+B,QAAQ,CAACf,UAAUY,eAAe,IAAI,EACxDL,WAAWS,OAAO,CACnB,CAAC;gBAEF,4CAA4C;gBAC5C,MAAMC,QAAQ,IAAIC;gBAElB,IAAIhB,QAAQ,CAACY,IAAI,EAAE;oBACjB,iDAAiD;oBACjD,kDAAkD;oBAClD,gDAAgD;oBAChD,2CAA2C;oBAC3C,6CAA6C;oBAC7C,KAAK,MAAMK,QAAQjB,QAAQ,CAACY,IAAI,CAACG,KAAK,CAAE;wBACtCA,MAAMG,GAAG,CAACD;oBACZ;gBACF;gBAEA,oDAAoD;gBACpD,qDAAqD;gBACrD,6BAA6B;gBAC7B,IAAIb,YAAY;oBACd,KAAK,MAAMe,SAAS,AAACf,WAClBgB,MAAM,CAAmC;wBAC1CD,MAAMJ,KAAK,CAACZ,OAAO,CAAC,CAACc;4BACnB,IACE,AAACA,CAAAA,KAAKI,QAAQ,CAAC,UAAUJ,KAAKI,QAAQ,CAAC,OAAM,KAC7CJ,KAAKK,KAAK,CAAC,4BACX;gCACAP,MAAMG,GAAG,CAACD;4BACZ;wBACF;oBACF;gBACF;gBAEA,qDAAqD;gBACrD,sDAAsD;gBACtD,wDAAwD;gBAExD,uCAAuC;gBACvC,MAAMM,KAAKxB,MAAMa,MAAM7B,YAAYC,aAAaC;gBAChDe,QAAQ,CAACY,IAAI,GAAG;oBAAEW;oBAAIR,OAAOS,MAAMC,IAAI,CAACV;gBAAO;YACjD;QACF;IACF;IACA,KAAK,MAAM9B,UAAUD,YAAY0C,OAAO,CAAE;QACxCzC,OAAOiB,MAAM,CAACC,OAAO,CAACF;IACxB;IAEAD,WAAW2B,OAAOC,IAAI,CAAC5B,UACpB6B,IAAI,EACL,wCAAwC;KACvCC,MAAM,CAAC,CAACC,GAAGC,IAAO,CAAA,AAACD,CAAC,CAACC,EAAE,GAAGhC,QAAQ,CAACgC,EAAE,EAAGD,CAAAA,GAAI,CAAC;IAEhD,OAAO/B;AACT;AAEA,OAAO,MAAMiC;IAMXC,YAAYC,IAKX,CAAE;QACD,IAAI,CAACC,QAAQ,GAAGD,KAAKC,QAAQ;QAC7B,IAAI,CAACtC,QAAQ,GAAGqC,KAAKrC,QAAQ;QAC7B,IAAI,CAACuC,YAAY,GAAGF,KAAKE,YAAY;QACrC,IAAI,CAACtC,GAAG,GAAGoC,KAAKpC,GAAG;IACrB;IAEAuC,aAAaC,QAAa,EAAEvD,WAAgB,EAAEwD,MAAW,EAAE;QACzD,MAAMxC,WAAWJ,cACf2C,UACAvD,aACA,IAAI,CAACc,QAAQ,EACb,IAAI,CAACC,GAAG;QAEV,oDAAoD;QACpDyC,MAAM,CAAC,IAAI,CAACJ,QAAQ,CAAC,GAAG,IAAIvD,QAAQ4D,SAAS,CAC3CC,KAAKC,SAAS,CAAC3C,UAAU,MAAM;QAEjC,IAAI,IAAI,CAACqC,YAAY,EAAE;YACrBG,MAAM,CAAC,IAAI,CAACH,YAAY,CAAC,GAAG,IAAIxD,QAAQ4D,SAAS,CAC/C,CAAC,+BAA+B,EAAEC,KAAKC,SAAS,CAC9CD,KAAKC,SAAS,CAAC3C,WACf,CAAC;QAEP;QACA,OAAOwC;IACT;IAEAI,MAAML,QAA0B,EAAE;QAChCA,SAASM,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,yBAAyB,CAAC/D;YAChDA,YAAY6D,KAAK,CAACG,aAAa,CAACD,GAAG,CACjC;gBACEE,MAAM;gBACNC,OAAOtE,QAAQuE,WAAW,CAACC,8BAA8B;YAC3D,GACA,CAACZ;gBACC,IAAI,CAACF,YAAY,CAACC,UAAUvD,aAAawD;YAC3C;QAEJ;IACF;AACF"}