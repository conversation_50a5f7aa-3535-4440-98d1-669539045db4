{"version": 3, "sources": ["../../../src/shared/lib/constants.ts"], "names": ["MODERN_BROWSERSLIST_TARGET", "COMPILER_NAMES", "INTERNAL_HEADERS", "COMPILER_INDEXES", "PHASE_EXPORT", "PHASE_PRODUCTION_BUILD", "PHASE_PRODUCTION_SERVER", "PHASE_DEVELOPMENT_SERVER", "PHASE_TEST", "PHASE_INFO", "PAGES_MANIFEST", "APP_PATHS_MANIFEST", "APP_PATH_ROUTES_MANIFEST", "BUILD_MANIFEST", "APP_BUILD_MANIFEST", "FUNCTIONS_CONFIG_MANIFEST", "SUBRESOURCE_INTEGRITY_MANIFEST", "NEXT_FONT_MANIFEST", "EXPORT_MARKER", "EXPORT_DETAIL", "PRERENDER_MANIFEST", "ROUTES_MANIFEST", "IMAGES_MANIFEST", "SERVER_FILES_MANIFEST", "DEV_CLIENT_PAGES_MANIFEST", "MIDDLEWARE_MANIFEST", "DEV_MIDDLEWARE_MANIFEST", "REACT_LOADABLE_MANIFEST", "FONT_MANIFEST", "SERVER_DIRECTORY", "CONFIG_FILES", "BUILD_ID_FILE", "BLOCKED_PAGES", "CLIENT_PUBLIC_FILES_PATH", "CLIENT_STATIC_FILES_PATH", "STRING_LITERAL_DROP_BUNDLE", "NEXT_BUILTIN_DOCUMENT", "BARREL_OPTIMIZATION_PREFIX", "CLIENT_REFERENCE_MANIFEST", "SERVER_REFERENCE_MANIFEST", "MIDDLEWARE_BUILD_MANIFEST", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "APP_CLIENT_INTERNALS", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "EDGE_RUNTIME_WEBPACK", "STATIC_PROPS_ID", "SERVER_PROPS_ID", "PAGE_SEGMENT_KEY", "GOOGLE_FONT_PROVIDER", "OPTIMIZED_FONT_PROVIDERS", "DEFAULT_SERIF_FONT", "DEFAULT_SANS_SERIF_FONT", "STATIC_STATUS_PAGES", "TRACE_OUTPUT_VERSION", "TURBO_TRACE_DEFAULT_MEMORY_LIMIT", "RSC_MODULE_TYPES", "EDGE_UNSUPPORTED_NODE_APIS", "SYSTEM_ENTRYPOINTS", "client", "server", "edgeServer", "Symbol", "url", "preconnect", "name", "xAvgCharWidth", "azAvgWidth", "unitsPerEm", "Set"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAESA,0BAA0B;eAA1BA,iCAA0B;;IAItBC,cAAc;eAAdA;;IAUAC,gBAAgB;eAAhBA;;IAWAC,gBAAgB;eAAhBA;;IAQAC,YAAY;eAAZA;;IACAC,sBAAsB;eAAtBA;;IACAC,uBAAuB;eAAvBA;;IACAC,wBAAwB;eAAxBA;;IACAC,UAAU;eAAVA;;IACAC,UAAU;eAAVA;;IACAC,cAAc;eAAdA;;IACAC,kBAAkB;eAAlBA;;IACAC,wBAAwB;eAAxBA;;IACAC,cAAc;eAAdA;;IACAC,kBAAkB;eAAlBA;;IACAC,yBAAyB;eAAzBA;;IACAC,8BAA8B;eAA9BA;;IACAC,kBAAkB;eAAlBA;;IACAC,aAAa;eAAbA;;IACAC,aAAa;eAAbA;;IACAC,kBAAkB;eAAlBA;;IACAC,eAAe;eAAfA;;IACAC,eAAe;eAAfA;;IACAC,qBAAqB;eAArBA;;IACAC,yBAAyB;eAAzBA;;IACAC,mBAAmB;eAAnBA;;IACAC,uBAAuB;eAAvBA;;IACAC,uBAAuB;eAAvBA;;IACAC,aAAa;eAAbA;;IACAC,gBAAgB;eAAhBA;;IACAC,YAAY;eAAZA;;IACAC,aAAa;eAAbA;;IACAC,aAAa;eAAbA;;IACAC,wBAAwB;eAAxBA;;IACAC,wBAAwB;eAAxBA;;IACAC,0BAA0B;eAA1BA;;IACAC,qBAAqB;eAArBA;;IACAC,0BAA0B;eAA1BA;;IAGAC,yBAAyB;eAAzBA;;IAEAC,yBAAyB;eAAzBA;;IAEAC,yBAAyB;eAAzBA;;IAEAC,kCAAkC;eAAlCA;;IAIAC,gCAAgC;eAAhCA;;IACAC,oCAAoC;eAApCA;;IAEAC,oBAAoB;eAApBA;;IAEAC,yCAAyC;eAAzCA;;IAEAC,+BAA+B;eAA/BA;;IAEAC,mCAAmC;eAAnCA;;IAEAC,qCAAqC;eAArCA;;IACAC,4CAA4C;eAA5CA;;IAGAC,oBAAoB;eAApBA;;IACAC,eAAe;eAAfA;;IACAC,eAAe;eAAfA;;IACAC,gBAAgB;eAAhBA;;IACAC,oBAAoB;eAApBA;;IACAC,wBAAwB;eAAxBA;;IAIAC,kBAAkB;eAAlBA;;IAMAC,uBAAuB;eAAvBA;;IAMAC,mBAAmB;eAAnBA;;IACAC,oBAAoB;eAApBA;;IAEAC,gCAAgC;eAAhCA;;IAEAC,gBAAgB;eAAhBA;;IASAC,0BAA0B;eAA1BA;;IAmBAC,kBAAkB;eAAlBA;;;;mFAtJ0B;AAMhC,MAAM9D,iBAAiB;IAC5B+D,QAAQ;IACRC,QAAQ;IACRC,YAAY;AACd;AAMO,MAAMhE,mBAAmB;IAC9B;IACA;IACA;IACA;IACA;IACA;CACD;AAIM,MAAMC,mBAET;IACF,CAACF,eAAe+D,MAAM,CAAC,EAAE;IACzB,CAAC/D,eAAegE,MAAM,CAAC,EAAE;IACzB,CAAChE,eAAeiE,UAAU,CAAC,EAAE;AAC/B;AAEO,MAAM9D,eAAe;AACrB,MAAMC,yBAAyB;AAC/B,MAAMC,0BAA0B;AAChC,MAAMC,2BAA2B;AACjC,MAAMC,aAAa;AACnB,MAAMC,aAAa;AACnB,MAAMC,iBAAiB;AACvB,MAAMC,qBAAqB;AAC3B,MAAMC,2BAA2B;AACjC,MAAMC,iBAAiB;AACvB,MAAMC,qBAAqB;AAC3B,MAAMC,4BAA4B;AAClC,MAAMC,iCAAiC;AACvC,MAAMC,qBAAqB;AAC3B,MAAMC,gBAAgB;AACtB,MAAMC,gBAAgB;AACtB,MAAMC,qBAAqB;AAC3B,MAAMC,kBAAkB;AACxB,MAAMC,kBAAkB;AACxB,MAAMC,wBAAwB;AAC9B,MAAMC,4BAA4B;AAClC,MAAMC,sBAAsB;AAC5B,MAAMC,0BAA0B;AAChC,MAAMC,0BAA0B;AAChC,MAAMC,gBAAgB;AACtB,MAAMC,mBAAmB;AACzB,MAAMC,eAAe;IAAC;IAAkB;CAAkB;AAC1D,MAAMC,gBAAgB;AACtB,MAAMC,gBAAgB;IAAC;IAAc;IAAS;CAAU;AACxD,MAAMC,2BAA2B;AACjC,MAAMC,2BAA2B;AACjC,MAAMC,6BAA6B;AACnC,MAAMC,wBAAwB;AAC9B,MAAMC,6BAA6B;AAGnC,MAAMC,4BAA4B;AAElC,MAAMC,4BAA4B;AAElC,MAAMC,4BAA4B;AAElC,MAAMC,qCACX;AAGK,MAAMC,mCAAoC;AAC1C,MAAMC,uCAAuC,AAAC,KAAED,mCAAiC;AAEjF,MAAME,uBAAuB;AAE7B,MAAMC,4CAA6C;AAEnD,MAAMC,kCAAmC;AAEzC,MAAMC,sCAAuC;AAE7C,MAAMC,wCAAwC;AAC9C,MAAMC,+CAA+CkB,OAC1DnB;AAEK,MAAME,uBAAuB;AAC7B,MAAMC,kBAAkB;AACxB,MAAMC,kBAAkB;AACxB,MAAMC,mBAAmB;AACzB,MAAMC,uBAAuB;AAC7B,MAAMC,2BAA2B;IACtC;QAAEa,KAAKd;QAAsBe,YAAY;IAA4B;IACrE;QAAED,KAAK;QAA2BC,YAAY;IAA0B;CACzE;AACM,MAAMb,qBAAqB;IAChCc,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd;AACO,MAAMhB,0BAA0B;IACrCa,MAAM;IACNC,eAAe;IACfC,YAAY;IACZC,YAAY;AACd;AACO,MAAMf,sBAAsB;IAAC;CAAO;AACpC,MAAMC,uBAAuB;AAE7B,MAAMC,mCAAmC;AAEzC,MAAMC,mBAAmB;IAC9BG,QAAQ;IACRC,QAAQ;AACV;AAMO,MAAMH,6BAA6B;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAMC,qBAAqB,IAAIW,IAAY;IAChDhC;IACAG;IACAC;IACAH;CACD"}