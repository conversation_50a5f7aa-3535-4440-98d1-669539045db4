{"version": 3, "sources": ["../../../../src/shared/lib/router/action-queue.ts"], "names": ["ActionQueueContext", "createMutableActionQueue", "React", "createContext", "runRemainingActions", "actionQueue", "setState", "pending", "next", "runAction", "action", "prevState", "state", "Error", "payload", "actionResult", "handleResult", "nextState", "discarded", "needsRefresh", "dispatch", "type", "ACTION_REFRESH", "origin", "window", "location", "devToolsInstance", "send", "resolve", "isThenable", "then", "err", "reject", "dispatchAction", "resolvers", "ACTION_RESTORE", "deferred<PERSON><PERSON><PERSON>", "Promise", "startTransition", "newAction", "last", "ACTION_NAVIGATE", "ACTION_SERVER_ACTION", "result", "reducer"], "mappings": ";;;;;;;;;;;;;;;IAkCaA,kBAAkB;eAAlBA;;IAuJGC,wBAAwB;eAAxBA;;;;oCAhLT;+BAEiB;iEACe;AAsBhC,MAAMD,qBACXE,cAAK,CAACC,aAAa,CAA8B;AAEnD,SAASC,oBACPC,WAAiC,EACjCC,QAA8B;IAE9B,IAAID,YAAYE,OAAO,KAAK,MAAM;QAChCF,YAAYE,OAAO,GAAGF,YAAYE,OAAO,CAACC,IAAI;QAC9C,IAAIH,YAAYE,OAAO,KAAK,MAAM;YAChC,mEAAmE;YACnEE,UAAU;gBACRJ;gBACAK,QAAQL,YAAYE,OAAO;gBAC3BD;YACF;QACF;IACF;AACF;AAEA,eAAeG,UAAU,KAQxB;IARwB,IAAA,EACvBJ,WAAW,EACXK,MAAM,EACNJ,QAAQ,EAKT,GARwB;IASvB,MAAMK,YAAYN,YAAYO,KAAK;IACnC,IAAI,CAACD,WAAW;QACd,sFAAsF;QACtF,MAAM,IAAIE,MAAM;IAClB;IAEAR,YAAYE,OAAO,GAAGG;IAEtB,MAAMI,UAAUJ,OAAOI,OAAO;IAC9B,MAAMC,eAAeV,YAAYK,MAAM,CAACC,WAAWG;IAEnD,SAASE,aAAaC,SAAyB;QAC7C,kEAAkE;QAClE,IAAIP,OAAOQ,SAAS,EAAE;YACpB,oFAAoF;YACpF,IAAIb,YAAYc,YAAY,IAAId,YAAYE,OAAO,KAAK,MAAM;gBAC5DF,YAAYc,YAAY,GAAG;gBAC3Bd,YAAYe,QAAQ,CAClB;oBACEC,MAAMC,kCAAc;oBACpBC,QAAQC,OAAOC,QAAQ,CAACF,MAAM;gBAChC,GACAjB;YAEJ;YACA;QACF;QAEAD,YAAYO,KAAK,GAAGK;QAEpB,IAAIZ,YAAYqB,gBAAgB,EAAE;YAChCrB,YAAYqB,gBAAgB,CAACC,IAAI,CAACb,SAASG;QAC7C;QAEAb,oBAAoBC,aAAaC;QACjCI,OAAOkB,OAAO,CAACX;IACjB;IAEA,8DAA8D;IAC9D,IAAIY,IAAAA,8BAAU,EAACd,eAAe;QAC5BA,aAAae,IAAI,CAACd,cAAc,CAACe;YAC/B3B,oBAAoBC,aAAaC;YACjCI,OAAOsB,MAAM,CAACD;QAChB;IACF,OAAO;QACLf,aAAaD;IACf;AACF;AAEA,SAASkB,eACP5B,WAAiC,EACjCS,OAAuB,EACvBR,QAA8B;IAE9B,IAAI4B,YAGA;QAAEN,SAAStB;QAAU0B,QAAQ,KAAO;IAAE;IAE1C,mEAAmE;IACnE,wFAAwF;IACxF,2DAA2D;IAC3D,oDAAoD;IACpD,IAAIlB,QAAQO,IAAI,KAAKc,kCAAc,EAAE;QACnC,6DAA6D;QAC7D,MAAMC,kBAAkB,IAAIC,QAAwB,CAACT,SAASI;YAC5DE,YAAY;gBAAEN;gBAASI;YAAO;QAChC;QAEAM,IAAAA,sBAAe,EAAC;YACd,oGAAoG;YACpG,iEAAiE;YACjEhC,SAAS8B;QACX;IACF;IAEA,MAAMG,YAA6B;QACjCzB;QACAN,MAAM;QACNoB,SAASM,UAAWN,OAAO;QAC3BI,QAAQE,UAAWF,MAAM;IAC3B;IAEA,8BAA8B;IAC9B,IAAI3B,YAAYE,OAAO,KAAK,MAAM;QAChC,iEAAiE;QACjE,4CAA4C;QAC5CF,YAAYmC,IAAI,GAAGD;QAEnB9B,UAAU;YACRJ;YACAK,QAAQ6B;YACRjC;QACF;IACF,OAAO,IAAIQ,QAAQO,IAAI,KAAKoB,mCAAe,EAAE;QAC3C,sDAAsD;QACtD,oHAAoH;QACpHpC,YAAYE,OAAO,CAACW,SAAS,GAAG;QAEhC,4CAA4C;QAC5Cb,YAAYmC,IAAI,GAAGD;QAEnB,2GAA2G;QAC3G,IAAIlC,YAAYE,OAAO,CAACO,OAAO,CAACO,IAAI,KAAKqB,wCAAoB,EAAE;YAC7DrC,YAAYc,YAAY,GAAG;QAC7B;QAEAV,UAAU;YACRJ;YACAK,QAAQ6B;YACRjC;QACF;IACF,OAAO;QACL,oEAAoE;QACpE,+EAA+E;QAC/E,IAAID,YAAYmC,IAAI,KAAK,MAAM;YAC7BnC,YAAYmC,IAAI,CAAChC,IAAI,GAAG+B;QAC1B;QACAlC,YAAYmC,IAAI,GAAGD;IACrB;AACF;AAEO,SAAStC;IACd,MAAMI,cAAoC;QACxCO,OAAO;QACPQ,UAAU,CAACN,SAAyBR,WAClC2B,eAAe5B,aAAaS,SAASR;QACvCI,QAAQ,OAAOE,OAAuBF;YACpC,IAAIE,UAAU,MAAM;gBAClB,MAAM,IAAIC,MAAM;YAClB;YACA,MAAM8B,SAASC,IAAAA,sBAAO,EAAChC,OAAOF;YAC9B,OAAOiC;QACT;QACApC,SAAS;QACTiC,MAAM;IACR;IAEA,OAAOnC;AACT"}