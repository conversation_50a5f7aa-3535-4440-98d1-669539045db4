# LegalPR - Puerto Rico Legal Marketplace 🏛️

A comprehensive legal marketplace platform connecting clients with qualified attorneys across Puerto Rico. Available as both a **Progressive Web App (PWA)** and **native mobile apps** for iOS and Android.

## 🌟 Platform Overview

### 🌐 **Web App (PWA) + 📱 Mobile Apps**
- **Cross-platform availability**: Web, iOS, and Android
- **Installable PWA**: Add to home screen on mobile devices
- **Offline support**: Core features work without internet
- **Push notifications**: Real-time updates across all platforms
- **Responsive design**: Optimized for all screen sizes

## ✨ Features

### For Clients 👥
- **Find Qualified Lawyers**: Search and filter attorneys by specialization, location, and ratings
- **Easy Appointment Booking**: Schedule consultations with integrated calendar system
- **Secure Messaging**: Direct communication with attorneys
- **Document Management**: Upload and share legal documents securely
- **Cost Estimation**: Get transparent pricing before booking
- **Review System**: Read and write reviews for attorneys
- **Multilingual Support**: Available in Spanish and English
- **Mobile Document Scanning**: Camera integration for document capture (mobile)
- **Biometric Authentication**: Secure login with fingerprint/Face ID (mobile)

### For Lawyers ⚖️
- **Professional Profiles**: Showcase expertise, education, and experience
- **Appointment Management**: Manage schedule and client appointments
- **Client Communication**: Secure messaging system
- **Payment Processing**: Integrated Stripe payments with automatic payouts
- **Document Sharing**: Secure document exchange with clients
- **Analytics Dashboard**: Track performance and earnings
- **Verification System**: Professional license verification
- **Mobile Office**: Full practice management on mobile devices

### For Administrators 🛡️
- **User Management**: Oversee all platform users
- **Lawyer Verification**: Validate attorney credentials and licenses
- **Content Moderation**: Review and moderate user-generated content
- **Analytics & Reporting**: Comprehensive platform analytics
- **Payment Management**: Oversee transactions and disputes

## 🛠 Technology Stack

### 🌐 **Web App (PWA)**
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **ShadCN UI** - Modern component library
- **Next-Intl** - Internationalization (Spanish/English)
- **Next-PWA** - Progressive Web App capabilities

### 📱 **Mobile Apps (React Native)**
- **Expo** - React Native development platform
- **React Navigation** - Navigation library
- **React Native Paper** - Material Design components
- **Expo Notifications** - Push notifications
- **Expo Camera** - Document scanning
- **Expo Secure Store** - Secure data storage
- **Stripe React Native** - Mobile payments

### 🔧 **Backend & Database**
- **Firebase Auth** - User authentication and authorization
- **Firestore** - NoSQL database for real-time data
- **Firebase Storage** - File storage for documents and images
- **Firebase Functions** - Serverless backend functions
- **Firebase Cloud Messaging** - Push notifications

### 💳 **Payments**
- **Stripe Connect** - Payment processing for lawyers
- **Stripe Webhooks** - Real-time payment notifications
- **Mobile payments** - Apple Pay and Google Pay support

## 🚀 Quick Start

### 🌐 Web App Setup

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **Access the app**
   - Web: http://localhost:3000
   - PWA: Install prompt will appear on mobile browsers

### 📱 Mobile App Setup

1. **Navigate to mobile directory**
   ```bash
   cd mobile
   ```

2. **Install Expo CLI and dependencies**
   ```bash
   npm install -g @expo/cli
   npm install
   ```

3. **Start the development server**
   ```bash
   npx expo start
   ```

4. **Run on devices**
   - **iOS**: Press `i` or scan QR code with Camera app
   - **Android**: Press `a` or scan QR code with Expo Go app

## 📱 Mobile App Features

### 🔔 **Push Notifications**
- Appointment reminders
- New message alerts
- Payment confirmations
- System notifications

### 📷 **Document Scanning**
- Camera integration for document capture
- Automatic edge detection
- PDF generation
- Secure cloud storage

### 🔐 **Security**
- Biometric authentication (Face ID, Touch ID, Fingerprint)
- Secure storage for sensitive data
- End-to-end encryption for messages
- Offline data protection

### 🌐 **Offline Support**
- View cached appointments
- Read previous messages
- Access saved documents
- Sync when connection restored

## 🏗 Project Structure

```
delawpr/
├── 🌐 Web App (PWA)
│   ├── app/                    # Next.js app directory
│   ├── components/            # React components
│   ├── functions/            # Firebase Functions
│   ├── lib/                  # Utility libraries
│   ├── public/               # Static assets & PWA manifest
│   └── types/                # TypeScript definitions
│
├── 📱 Mobile App (React Native)
│   ├── screens/              # App screens
│   ├── navigation/           # Navigation configuration
│   ├── lib/                  # Utilities and services
│   ├── components/           # React Native components
│   └── config/               # App configuration
│
└── 🔧 Shared
    ├── firebase.json         # Firebase configuration
    ├── firestore.rules      # Database security rules
    └── storage.rules        # Storage security rules
```

## 🌐 Deployment

### 🌍 **Web App (Vercel/Netlify)**
```bash
npm run build
npm run start
```

### 📱 **Mobile App (App Stores)**
```bash
cd mobile
eas build --platform all
eas submit --platform all
```

### 🏗️ **PWA Installation**
Users can install the PWA by:
- **iOS Safari**: Share button → "Add to Home Screen"
- **Android Chrome**: Install prompt or menu → "Add to Home Screen"
- **Desktop**: Install button in address bar

## 🔒 Security Features

- **Firebase Security Rules** for database and storage
- **Secure authentication** with Firebase Auth
- **HTTPS enforcement** across all platforms
- **Data encryption** for sensitive information
- **Biometric authentication** on mobile devices
- **Secure storage** for offline data

## 📊 Analytics & Monitoring

- **User engagement** metrics across web and mobile
- **Appointment booking** rates and conversion
- **Revenue tracking** and commission analytics
- **Lawyer performance** metrics
- **Geographic usage** patterns
- **Platform comparison** (web vs mobile usage)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL>

---

**LegalPR** - Connecting Puerto Rico's legal community across all platforms. ⚖️📱🌐
