'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Scale, 
  Menu, 
  X, 
  Search, 
  Globe,
  Phone,
  Mail,
  ChevronDown
} from 'lucide-react';

export default function NavigationSimple() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' 
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-900 to-blue-700 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                <Scale className="h-6 w-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-blue-900">PR</span>
              </div>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-900">LegalPR</div>
              <div className="text-xs text-gray-500 -mt-1">Justicia para todos</div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            <Link href="/services" className="text-gray-700 hover:text-blue-900 font-medium px-4 py-2 rounded-md hover:bg-blue-50 transition-colors">
              Servicios Legales
            </Link>
            <Link href="/lawyers" className="text-gray-700 hover:text-blue-900 font-medium px-4 py-2 rounded-md hover:bg-blue-50 transition-colors">
              Buscar Abogados
            </Link>
            <Link href="/about" className="text-gray-700 hover:text-blue-900 font-medium px-4 py-2 rounded-md hover:bg-blue-50 transition-colors">
              Nosotros
            </Link>
            <Link href="/help" className="text-gray-700 hover:text-blue-900 font-medium px-4 py-2 rounded-md hover:bg-blue-50 transition-colors">
              Ayuda
            </Link>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <Button variant="ghost" size="sm" className="hidden md:flex text-gray-600 hover:text-blue-900">
              <Search className="h-5 w-5" />
            </Button>

            {/* Language Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="hidden md:flex text-gray-600 hover:text-blue-900">
                  <Globe className="h-5 w-5 mr-1" />
                  ES
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <span className="mr-2">🇪🇸</span>
                  Español
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <span className="mr-2">🇺🇸</span>
                  English
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Contact Info */}
            <div className="hidden xl:flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2 text-gray-600">
                <Phone className="h-4 w-4" />
                <span>(*************</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-600">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>

            {/* Auth Buttons */}
            <div className="hidden lg:flex items-center space-x-3">
              <Button variant="ghost" className="text-gray-700 hover:text-blue-900">
                Iniciar Sesión
              </Button>
              <Button className="bg-gradient-to-r from-blue-900 to-blue-800 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                Registrarse
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg">
            <div className="px-4 py-6 space-y-4">
              <Link href="/services" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Servicios Legales
              </Link>
              <Link href="/lawyers" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Buscar Abogados
              </Link>
              <Link href="/about" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Nosotros
              </Link>
              <Link href="/help" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Ayuda
              </Link>
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <Button variant="ghost" className="w-full justify-start text-gray-700">
                  Iniciar Sesión
                </Button>
                <Button className="w-full bg-gradient-to-r from-blue-900 to-blue-800 text-white font-semibold py-2 px-4 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300">
                  Registrarse
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
