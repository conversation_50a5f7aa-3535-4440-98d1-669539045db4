'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Scale, 
  Menu, 
  X, 
  Search, 
  Bell, 
  User, 
  Globe, 
  Phone,
  Mail,
  MapPin,
  Shield,
  Award,
  Users,
  FileText,
  Calendar,
  MessageSquare,
  Settings,
  LogOut,
  ChevronDown
} from 'lucide-react';

const legalServices = [
  {
    title: 'Derecho de Familia',
    description: 'Divorcio, custodia, adopción y pensión alimentaria',
    icon: Users,
    href: '/services/family-law',
    featured: true
  },
  {
    title: 'Derecho Inmobiliario',
    description: 'Compra, venta y disputas de propiedades',
    icon: MapPin,
    href: '/services/real-estate',
    featured: true
  },
  {
    title: 'Derecho Penal',
    description: 'Defensa criminal y representación legal',
    icon: Shield,
    href: '/services/criminal-law',
    featured: true
  },
  {
    title: 'Derecho Laboral',
    description: 'Disputas laborales y derechos de trabajadores',
    icon: FileText,
    href: '/services/employment-law'
  },
  {
    title: 'Derecho Corporativo',
    description: 'Formación de empresas y contratos',
    icon: Award,
    href: '/services/corporate-law'
  },
  {
    title: 'Lesiones Personales',
    description: 'Accidentes y negligencia médica',
    icon: Users,
    href: '/services/personal-injury'
  }
];

const resources = [
  {
    title: 'Centro de Ayuda',
    description: 'Guías y preguntas frecuentes',
    href: '/help'
  },
  {
    title: 'Blog Legal',
    description: 'Artículos y consejos legales',
    href: '/blog'
  },
  {
    title: 'Calculadora de Costos',
    description: 'Estima el costo de tu caso',
    href: '/cost-calculator'
  },
  {
    title: 'Directorio de Abogados',
    description: 'Encuentra abogados por especialidad',
    href: '/lawyers'
  }
];

export default function NavigationProfessional() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled 
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200' 
        : 'bg-transparent'
    }`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-900 to-blue-700 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                <Scale className="h-6 w-6 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                <span className="text-xs font-bold text-blue-900">PR</span>
              </div>
            </div>
            <div>
              <div className="text-xl font-bold text-gray-900">LegalPR</div>
              <div className="text-xs text-gray-500 -mt-1">Justicia para todos</div>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <NavigationMenu className="hidden lg:flex">
            <NavigationMenuList className="space-x-2">
              {/* Services Mega Menu */}
              <NavigationMenuItem>
                <NavigationMenuTrigger className="text-gray-700 hover:text-blue-900 font-medium">
                  Servicios Legales
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div className="w-[800px] p-6">
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Servicios Principales</h3>
                        <div className="space-y-3">
                          {legalServices.filter(service => service.featured).map((service) => (
                            <Link
                              key={service.href}
                              href={service.href}
                              className="flex items-start space-x-3 p-3 rounded-lg hover:bg-blue-50 transition-colors group"
                            >
                              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                                <service.icon className="h-5 w-5 text-blue-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 group-hover:text-blue-900">
                                  {service.title}
                                </div>
                                <div className="text-sm text-gray-500 mt-1">
                                  {service.description}
                                </div>
                              </div>
                            </Link>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-4">Más Servicios</h3>
                        <div className="space-y-3">
                          {legalServices.filter(service => !service.featured).map((service) => (
                            <Link
                              key={service.href}
                              href={service.href}
                              className="flex items-start space-x-3 p-3 rounded-lg hover:bg-blue-50 transition-colors group"
                            >
                              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                                <service.icon className="h-5 w-5 text-gray-600 group-hover:text-blue-600" />
                              </div>
                              <div>
                                <div className="font-medium text-gray-900 group-hover:text-blue-900">
                                  {service.title}
                                </div>
                                <div className="text-sm text-gray-500 mt-1">
                                  {service.description}
                                </div>
                              </div>
                            </Link>
                          ))}
                        </div>
                        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                          <div className="text-sm font-medium text-blue-900 mb-2">¿No encuentras tu especialidad?</div>
                          <Link href="/services" className="text-sm text-blue-600 hover:text-blue-800 font-medium">
                            Ver todos los servicios →
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* Find Lawyers */}
              <NavigationMenuItem>
                <Link href="/lawyers" className="text-gray-700 hover:text-blue-900 font-medium px-4 py-2 rounded-md hover:bg-blue-50 transition-colors">
                  Buscar Abogados
                </Link>
              </NavigationMenuItem>

              {/* Resources */}
              <NavigationMenuItem>
                <NavigationMenuTrigger className="text-gray-700 hover:text-blue-900 font-medium">
                  Recursos
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <div className="w-[400px] p-6">
                    <div className="space-y-3">
                      {resources.map((resource) => (
                        <Link
                          key={resource.href}
                          href={resource.href}
                          className="block p-3 rounded-lg hover:bg-blue-50 transition-colors group"
                        >
                          <div className="font-medium text-gray-900 group-hover:text-blue-900">
                            {resource.title}
                          </div>
                          <div className="text-sm text-gray-500 mt-1">
                            {resource.description}
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* About */}
              <NavigationMenuItem>
                <Link href="/about" className="text-gray-700 hover:text-blue-900 font-medium px-4 py-2 rounded-md hover:bg-blue-50 transition-colors">
                  Nosotros
                </Link>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <Button variant="ghost" size="sm" className="hidden md:flex text-gray-600 hover:text-blue-900">
              <Search className="h-5 w-5" />
            </Button>

            {/* Language Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="hidden md:flex text-gray-600 hover:text-blue-900">
                  <Globe className="h-5 w-5 mr-1" />
                  ES
                  <ChevronDown className="h-3 w-3 ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <span className="mr-2">🇪🇸</span>
                  Español
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <span className="mr-2">🇺🇸</span>
                  English
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Contact Info */}
            <div className="hidden xl:flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2 text-gray-600">
                <Phone className="h-4 w-4" />
                <span>(*************</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-600">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>

            {/* Auth Buttons */}
            <div className="hidden lg:flex items-center space-x-3">
              <Button variant="ghost" className="text-gray-700 hover:text-blue-900">
                Iniciar Sesión
              </Button>
              <Button className="btn-legal">
                Registrarse
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isOpen && (
          <div className="lg:hidden absolute top-full left-0 right-0 bg-white border-b border-gray-200 shadow-lg">
            <div className="px-4 py-6 space-y-4">
              <Link href="/services" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Servicios Legales
              </Link>
              <Link href="/lawyers" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Buscar Abogados
              </Link>
              <Link href="/about" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Nosotros
              </Link>
              <Link href="/help" className="block text-gray-700 hover:text-blue-900 font-medium py-2">
                Ayuda
              </Link>
              <div className="pt-4 border-t border-gray-200 space-y-3">
                <Button variant="ghost" className="w-full justify-start text-gray-700">
                  Iniciar Sesión
                </Button>
                <Button className="w-full btn-legal">
                  Registrarse
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
