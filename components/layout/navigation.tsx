'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Scale, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const t = useTranslations('navigation');

  return (
    <nav className="bg-white shadow-lg fixed w-full z-50 border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <Scale className="h-8 w-8 text-blue-700" />
              <span className="ml-2 text-xl font-bold text-gray-900">LegalPR</span>
            </div>
            <div className="hidden md:ml-8 md:flex md:space-x-8">
              <Link 
                href="#" 
                className="border-b-2 border-blue-600 text-blue-600 inline-flex items-center px-1 pt-1 text-sm font-semibold"
              >
                {t('home')}
              </Link>
              <Link 
                href="#services" 
                className="border-transparent border-b-2 hover:border-gray-300 text-gray-600 hover:text-gray-900 inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors"
              >
                {t('services')}
              </Link>
              <Link 
                href="#how-it-works" 
                className="border-transparent border-b-2 hover:border-gray-300 text-gray-600 hover:text-gray-900 inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors"
              >
                {t('howItWorks')}
              </Link>
              <Link 
                href="#testimonials" 
                className="border-transparent border-b-2 hover:border-gray-300 text-gray-600 hover:text-gray-900 inline-flex items-center px-1 pt-1 text-sm font-medium transition-colors"
              >
                {t('testimonials')}
              </Link>
            </div>
          </div>
          <div className="flex items-center">
            <div className="flex-shrink-0 space-x-3">
              <Button variant="outline">
                {t('signIn')}
              </Button>
              <Button variant="legal">
                {t('signUp')}
              </Button>
            </div>
            <div className="ml-4 md:hidden flex items-center">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                <span className="sr-only">Abrir menú principal</span>
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
            <Link 
              href="#" 
              className="bg-blue-50 border-blue-500 text-blue-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
            >
              {t('home')}
            </Link>
            <Link 
              href="#services" 
              className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
            >
              {t('services')}
            </Link>
            <Link 
              href="#how-it-works" 
              className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
            >
              {t('howItWorks')}
            </Link>
            <Link 
              href="#testimonials" 
              className="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium"
            >
              {t('testimonials')}
            </Link>
          </div>
          <div className="pt-4 pb-3 border-t border-gray-200">
            <div className="flex flex-col items-center px-4 space-y-3">
              <Button variant="outline" className="w-full">
                {t('signIn')}
              </Button>
              <Button variant="legal" className="w-full">
                {t('signUp')}
              </Button>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
}
