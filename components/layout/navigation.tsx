'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Scale, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const t = useTranslations('navigation');

  return (
    <nav className="bg-gradient-to-r from-blue-900 to-blue-700 fixed w-full z-50 backdrop-blur-md bg-opacity-90">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-20">
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <Scale className="h-10 w-10 text-white" />
              <span className="ml-3 text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-white to-blue-100">
                LegalPR
              </span>
            </div>
            <div className="hidden md:ml-12 md:flex md:space-x-8">
              <Link 
                href="#" 
                className="relative group text-white px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                {t('home')}
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
              <Link 
                href="#services" 
                className="relative group text-white/90 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                {t('services')}
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
              <Link 
                href="#how-it-works" 
                className="relative group text-white/90 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                {t('howItWorks')}
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
              <Link 
                href="#testimonials" 
                className="relative group text-white/90 px-3 py-2 rounded-lg transition-all duration-200 hover:bg-white/20"
              >
                {t('testimonials')}
                <span className="absolute bottom-1 left-1/2 w-0 h-0.5 bg-white group-hover:w-4/5 group-hover:left-[10%] transition-all duration-300"></span>
              </Link>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button 
              variant="ghost" 
              className="hidden md:inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm transition-all duration-200"
            >
              {t('login')}
            </Button>
            <Button className="hidden md:inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-lg text-blue-900 bg-white hover:bg-blue-50 transition-all duration-200 shadow-lg shadow-blue-500/20">
              {t('getStarted')}
            </Button>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Button
                  variant="ghost"
                  className="md:hidden p-2 rounded-lg text-white hover:bg-white/20 focus:outline-none transition-colors"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                >
                  <span className="sr-only">Open main menu</span>
                  {isMobileMenuOpen ? (
                    <X className="block h-6 w-6" aria-hidden="true" />
                  ) : (
                    <Menu className="block h-6 w-6" aria-hidden="true" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className={`md:hidden transition-all duration-300 ease-in-out ${isMobileMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="pt-2 pb-4 space-y-1 bg-gradient-to-b from-blue-800 to-blue-900 shadow-xl">
          <Link
            href="#"
            className="block px-6 py-3 text-base font-medium text-white hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            {t('home')}
          </Link>
          <Link
            href="#services"
            className="block px-6 py-3 text-base font-medium text-white/90 hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            {t('services')}
          </Link>
          <Link
            href="#how-it-works"
            className="block px-6 py-3 text-base font-medium text-white/90 hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            {t('howItWorks')}
          </Link>
          <Link
            href="#testimonials"
            className="block px-6 py-3 text-base font-medium text-white/90 hover:bg-white/10 transition-colors"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            {t('testimonials')}
          </Link>
          <div className="pt-4 pb-3 border-t border-white/10 px-4 mt-2">
            <div className="space-y-3">
              <Button 
                variant="ghost" 
                className="w-full justify-start px-6 py-3 text-base font-medium text-white hover:bg-white/10"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {t('login')}
              </Button>
              <Button 
                className="w-full justify-center px-6 py-3 text-base font-medium bg-white text-blue-900 hover:bg-blue-50"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {t('getStarted')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
