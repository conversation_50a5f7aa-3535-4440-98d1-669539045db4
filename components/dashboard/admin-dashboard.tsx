'use client';

import React from 'react';
import { useAuth } from '@/components/providers/auth-provider';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Shield, 
  AlertTriangle, 
  TrendingUp, 
  DollarSign,
  Calendar,
  MessageSquare,
  FileText,
  BarChart3,
  Settings
} from 'lucide-react';

export function AdminDashboard() {
  const { user } = useAuth();

  return (
    <div className="p-6">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Panel de Administración
        </h1>
        <p className="mt-2 text-gray-600">
          Gestiona la plataforma LegalPR y supervisa todas las operaciones.
        </p>
      </div>

      {/* Platform Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usuarios</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,247</div>
            <p className="text-xs text-muted-foreground">
              +12% desde el mes pasado
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Abogados Activos</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <p className="text-xs text-muted-foreground">
              +5 nuevos esta semana
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Citas del Mes</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">342</div>
            <p className="text-xs text-muted-foreground">
              +23% vs mes anterior
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ingresos Plataforma</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$12,450</div>
            <p className="text-xs text-muted-foreground">
              Comisiones del mes
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Pending Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Acciones Pendientes</CardTitle>
            <CardDescription>
              Elementos que requieren tu atención
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 border rounded-lg bg-red-50">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    3 Abogados pendientes de verificación
                  </p>
                  <p className="text-sm text-gray-500">
                    Licencias por validar
                  </p>
                </div>
                <Button size="sm" variant="destructive">
                  Revisar
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg bg-orange-50">
                <div className="flex-shrink-0">
                  <MessageSquare className="h-8 w-8 text-orange-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    5 Reportes de usuarios
                  </p>
                  <p className="text-sm text-gray-500">
                    Disputas y quejas
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  Gestionar
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg bg-yellow-50">
                <div className="flex-shrink-0">
                  <FileText className="h-8 w-8 text-yellow-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    2 Reseñas flagged
                  </p>
                  <p className="text-sm text-gray-500">
                    Contenido inapropiado
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  Moderar
                </Button>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg bg-blue-50">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-blue-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    1 Disputa de pago
                  </p>
                  <p className="text-sm text-gray-500">
                    Reembolso solicitado
                  </p>
                </div>
                <Button size="sm" variant="outline">
                  Resolver
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Actividad Reciente</CardTitle>
            <CardDescription>
              Últimas acciones en la plataforma
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <Shield className="h-8 w-8 text-green-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Nuevo abogado verificado
                  </p>
                  <p className="text-sm text-gray-500">
                    Lic. Roberto Sánchez - Derecho Penal
                  </p>
                  <p className="text-xs text-gray-400">
                    Hace 1 hora
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-blue-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    15 nuevos usuarios registrados
                  </p>
                  <p className="text-sm text-gray-500">
                    Hoy
                  </p>
                  <p className="text-xs text-gray-400">
                    Hace 3 horas
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Pago procesado
                  </p>
                  <p className="text-sm text-gray-500">
                    $450 - Comisión de plataforma
                  </p>
                  <p className="text-xs text-gray-400">
                    Hace 5 horas
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4 p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    Reporte de usuario
                  </p>
                  <p className="text-sm text-gray-500">
                    Queja sobre Lic. María Torres
                  </p>
                  <p className="text-xs text-gray-400">
                    Ayer
                  </p>
                </div>
              </div>
            </div>

            <Button className="w-full mt-4" variant="outline">
              Ver Todo el Log de Actividad
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Gestión Rápida</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Button variant="legal" className="h-16">
            <Users className="mr-2 h-5 w-5" />
            Gestionar Usuarios
          </Button>
          <Button variant="outline" className="h-16">
            <Shield className="mr-2 h-5 w-5" />
            Verificar Abogados
          </Button>
          <Button variant="outline" className="h-16">
            <BarChart3 className="mr-2 h-5 w-5" />
            Ver Reportes
          </Button>
          <Button variant="outline" className="h-16">
            <Settings className="mr-2 h-5 w-5" />
            Configuración
          </Button>
        </div>
      </div>
    </div>
  );
}
