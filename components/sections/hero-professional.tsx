'use client';

import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Scale, 
  Shield, 
  Users, 
  Award, 
  TrendingUp, 
  MapPin, 
  Star,
  ArrowRight,
  CheckCircle,
  Play,
  Globe,
  Clock,
  Phone
} from 'lucide-react';
import Link from 'next/link';

const stats = [
  { label: 'Abogados Verificados', value: '500+', icon: Shield },
  { label: 'Casos Resueltos', value: '15,000+', icon: Scale },
  { label: 'Clientes Satisfecho<PERSON>', value: '12,000+', icon: Users },
  { label: 'Años de Experiencia', value: '25+', icon: Award },
];

const features = [
  {
    icon: Shield,
    title: 'Abogados Verificados',
    description: 'Todos nuestros abogados están licenciados y verificados por el Colegio de Abogados de Puerto Rico'
  },
  {
    icon: Clock,
    title: 'Respuesta Rápida',
    description: 'Obtén respuestas a tus consultas legales en menos de 24 horas'
  },
  {
    icon: Globe,
    title: 'Cobertura Total',
    description: 'Servicios legales en toda la isla, desde San Juan hasta Ponce y Mayagüez'
  },
  {
    icon: Phone,
    title: 'Soporte 24/7',
    description: 'Asistencia disponible las 24 horas para emergencias legales'
  }
];

const testimonials = [
  {
    name: 'María González',
    role: 'Empresaria',
    content: 'LegalPR me ayudó a encontrar el abogado perfecto para mi divorcio. El proceso fue transparente y profesional.',
    rating: 5,
    location: 'San Juan'
  },
  {
    name: 'Carlos Rodríguez',
    role: 'Propietario',
    content: 'Excelente plataforma para asuntos inmobiliarios. Mi abogado fue muy competente y el precio fue justo.',
    rating: 5,
    location: 'Bayamón'
  },
  {
    name: 'Ana Martínez',
    role: 'Trabajadora',
    content: 'Resolví mi caso laboral de manera eficiente. Recomiendo LegalPR a cualquiera que necesite ayuda legal.',
    rating: 5,
    location: 'Ponce'
  }
];

export default function HeroProfessional() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float"></div>
        <div className="absolute top-40 right-10 w-72 h-72 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        {/* Header Badge */}
        <div className="text-center mb-8 animate-fade-in">
          <Badge variant="secondary" className="px-4 py-2 text-sm font-medium bg-blue-100 text-blue-800 border-blue-200">
            🏛️ Plataforma Legal #1 en Puerto Rico
          </Badge>
        </div>

        {/* Main Hero Content */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-20">
          {/* Left Column - Content */}
          <div className="space-y-8 animate-slide-up">
            <div>
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                Conecta con los
                <span className="legal-text-gradient block">
                  Mejores Abogados
                </span>
                de Puerto Rico
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed mb-8">
                Encuentra abogados especializados, programa consultas y resuelve tus asuntos legales 
                de manera eficiente y transparente. Tu justicia, nuestra prioridad.
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="btn-legal group">
                Buscar Abogados
                <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </Button>
              <Button size="lg" variant="outline" className="border-2 border-blue-900 text-blue-900 hover:bg-blue-900 hover:text-white">
                <Play className="mr-2 h-5 w-5" />
                Ver Demo
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="flex items-center space-x-6 pt-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-600">Verificación Garantizada</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-600">Pagos Seguros</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-600">Soporte 24/7</span>
              </div>
            </div>
          </div>

          {/* Right Column - Interactive Elements */}
          <div className="relative animate-fade-in" style={{ animationDelay: '0.3s' }}>
            {/* Main Card */}
            <Card className="card-hover bg-white/80 backdrop-blur-sm border-0 shadow-2xl">
              <CardContent className="p-8">
                <div className="text-center mb-6">
                  <div className="w-16 h-16 bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Scale className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Consulta Gratuita</h3>
                  <p className="text-gray-600">Obtén asesoría legal inicial sin costo</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="font-medium text-gray-900">Derecho de Familia</span>
                    <Badge className="bg-green-100 text-green-800">Disponible</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="font-medium text-gray-900">Derecho Inmobiliario</span>
                    <Badge className="bg-green-100 text-green-800">Disponible</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <span className="font-medium text-gray-900">Derecho Laboral</span>
                    <Badge className="bg-yellow-100 text-yellow-800">2 disponibles</Badge>
                  </div>
                </div>

                <Button className="w-full mt-6 btn-legal">
                  Programar Consulta Gratuita
                </Button>
              </CardContent>
            </Card>

            {/* Floating Stats */}
            <div className="absolute -top-4 -left-4 bg-white rounded-lg shadow-lg p-4 animate-float">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">98% Éxito</p>
                  <p className="text-xs text-gray-500">Casos resueltos</p>
                </div>
              </div>
            </div>

            <div className="absolute -bottom-4 -right-4 bg-white rounded-lg shadow-lg p-4 animate-float" style={{ animationDelay: '1s' }}>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <MapPin className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">78 Municipios</p>
                  <p className="text-xs text-gray-500">Cobertura total</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center p-6 card-hover bg-white/60 backdrop-blur-sm border-0">
              <CardContent className="p-0">
                <div className="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {features.map((feature, index) => (
            <Card key={index} className="p-6 card-hover bg-white/60 backdrop-blur-sm border-0">
              <CardContent className="p-0">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Testimonials */}
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl p-8">
          <CardContent className="p-0">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Lo que dicen nuestros clientes</h2>
              <p className="text-gray-600">Testimonios reales de personas que han encontrado justicia</p>
            </div>

            <div className="max-w-4xl mx-auto">
              <div className="text-center">
                <div className="flex justify-center mb-4">
                  {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-xl text-gray-700 italic mb-6 leading-relaxed">
                  "{testimonials[currentTestimonial].content}"
                </blockquote>
                <div>
                  <div className="font-semibold text-gray-900">{testimonials[currentTestimonial].name}</div>
                  <div className="text-gray-600">{testimonials[currentTestimonial].role} • {testimonials[currentTestimonial].location}</div>
                </div>
              </div>

              <div className="flex justify-center mt-8 space-x-2">
                {testimonials.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentTestimonial ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
