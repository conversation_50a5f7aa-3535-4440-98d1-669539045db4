'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Users, Scale } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function CTASection() {
  const t = useTranslations('cta');

  return (
    <section className="bg-gradient-to-r from-blue-700 to-blue-800">
      <div className="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-20 lg:px-8 lg:flex lg:items-center lg:justify-between">
        <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
          <span className="block">{t('title')}</span>
          <span className="block text-blue-200">{t('subtitle')}</span>
        </h2>
        <div className="mt-8 flex flex-col sm:flex-row gap-4 lg:mt-0 lg:flex-shrink-0">
          <Button variant="outline" size="lg" className="bg-white text-blue-600 hover:bg-blue-50 shadow-lg transition-all duration-200 transform hover:-translate-y-1">
            <Users className="mr-2 h-5 w-5" />
            Buscar Abogado
          </Button>
          <Button variant="legal" size="lg" className="bg-blue-800 hover:bg-blue-900 shadow-lg transition-all duration-200 transform hover:-translate-y-1">
            <Scale className="mr-2 h-5 w-5" />
            Únete como Abogado
          </Button>
        </div>
      </div>
    </section>
  );
}
