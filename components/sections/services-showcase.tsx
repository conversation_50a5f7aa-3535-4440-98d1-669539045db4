'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Scale, 
  Shield, 
  Users, 
  Home, 
  Building, 
  Heart, 
  FileText, 
  DollarSign,
  Globe,
  Briefcase,
  Award,
  TrendingUp,
  Clock,
  Star,
  ArrowRight,
  CheckCircle,
  MapPin
} from 'lucide-react';

const legalCategories = [
  {
    id: 'family',
    title: 'Derecho de Familia',
    icon: Heart,
    description: 'Protegemos lo que más importa: tu familia',
    color: 'bg-rose-500',
    lightColor: 'bg-rose-50',
    textColor: 'text-rose-600',
    cases: '2,847',
    successRate: '94%',
    avgCost: '$1,200 - $3,500',
    timeframe: '2-6 meses',
    services: [
      'Divorcio y separación legal',
      'Custodia y patria potestad',
      'Pensión alimentaria',
      'Adopción y tutela',
      'Violencia doméstica',
      'Acuerdos prematrimoniales'
    ],
    lawyers: 89,
    featured: true
  },
  {
    id: 'real-estate',
    title: 'Derecho Inmobiliario',
    icon: Home,
    description: 'Tu patrimonio inmobiliario en manos expertas',
    color: 'bg-green-500',
    lightColor: 'bg-green-50',
    textColor: 'text-green-600',
    cases: '1,923',
    successRate: '97%',
    avgCost: '$800 - $2,500',
    timeframe: '1-3 meses',
    services: [
      'Compra y venta de propiedades',
      'Títulos de propiedad',
      'Disputas de linderos',
      'Contratos de arrendamiento',
      'Zonificación y permisos',
      'Ejecuciones hipotecarias'
    ],
    lawyers: 67,
    featured: true
  },
  {
    id: 'criminal',
    title: 'Derecho Penal',
    icon: Shield,
    description: 'Defensa sólida para proteger tus derechos',
    color: 'bg-red-500',
    lightColor: 'bg-red-50',
    textColor: 'text-red-600',
    cases: '3,156',
    successRate: '91%',
    avgCost: '$2,000 - $8,000',
    timeframe: '3-12 meses',
    services: [
      'Defensa criminal',
      'Delitos federales',
      'Violaciones de tránsito',
      'Delitos de drogas',
      'Violencia doméstica',
      'Apelaciones penales'
    ],
    lawyers: 45,
    featured: true
  },
  {
    id: 'employment',
    title: 'Derecho Laboral',
    icon: Users,
    description: 'Protegemos tus derechos como trabajador',
    color: 'bg-blue-500',
    lightColor: 'bg-blue-50',
    textColor: 'text-blue-600',
    cases: '1,567',
    successRate: '89%',
    avgCost: '$1,500 - $4,000',
    timeframe: '2-8 meses',
    services: [
      'Despidos injustificados',
      'Discriminación laboral',
      'Acoso en el trabajo',
      'Salarios no pagados',
      'Compensación por accidentes',
      'Contratos laborales'
    ],
    lawyers: 52,
    featured: false
  },
  {
    id: 'corporate',
    title: 'Derecho Corporativo',
    icon: Building,
    description: 'Soluciones legales para tu empresa',
    color: 'bg-purple-500',
    lightColor: 'bg-purple-50',
    textColor: 'text-purple-600',
    cases: '892',
    successRate: '96%',
    avgCost: '$2,500 - $10,000',
    timeframe: '1-6 meses',
    services: [
      'Formación de empresas',
      'Contratos comerciales',
      'Fusiones y adquisiciones',
      'Propiedad intelectual',
      'Cumplimiento regulatorio',
      'Disputas comerciales'
    ],
    lawyers: 34,
    featured: false
  },
  {
    id: 'personal-injury',
    title: 'Lesiones Personales',
    icon: Briefcase,
    description: 'Compensación justa por tus lesiones',
    color: 'bg-orange-500',
    lightColor: 'bg-orange-50',
    textColor: 'text-orange-600',
    cases: '2,234',
    successRate: '93%',
    avgCost: 'Sin costo inicial',
    timeframe: '6-18 meses',
    services: [
      'Accidentes automovilísticos',
      'Negligencia médica',
      'Accidentes de trabajo',
      'Productos defectuosos',
      'Caídas y resbalones',
      'Muerte por negligencia'
    ],
    lawyers: 41,
    featured: false
  }
];

const processSteps = [
  {
    step: '01',
    title: 'Consulta Inicial',
    description: 'Describe tu caso y recibe asesoría gratuita de 30 minutos',
    icon: FileText
  },
  {
    step: '02',
    title: 'Encuentra tu Abogado',
    description: 'Te conectamos con abogados especializados en tu área',
    icon: Users
  },
  {
    step: '03',
    title: 'Programa tu Cita',
    description: 'Agenda una consulta en persona o virtual',
    icon: Clock
  },
  {
    step: '04',
    title: 'Resuelve tu Caso',
    description: 'Tu abogado trabaja para obtener el mejor resultado',
    icon: Award
  }
];

export default function ServicesShowcase() {
  const [selectedCategory, setSelectedCategory] = useState('family');
  const selectedService = legalCategories.find(cat => cat.id === selectedCategory);

  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-blue-100 text-blue-800">
            Servicios Legales Especializados
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Expertos en Todas las
            <span className="legal-text-gradient block">Áreas del Derecho</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Más de 500 abogados especializados listos para ayudarte. 
            Encuentra el experto perfecto para tu caso específico.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {legalCategories.map((category) => (
            <Card 
              key={category.id} 
              className={`card-hover cursor-pointer transition-all duration-300 ${
                selectedCategory === category.id 
                  ? 'ring-2 ring-blue-500 shadow-xl' 
                  : 'hover:shadow-xl'
              }`}
              onClick={() => setSelectedCategory(category.id)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 ${category.lightColor} rounded-xl flex items-center justify-center`}>
                    <category.icon className={`h-6 w-6 ${category.textColor}`} />
                  </div>
                  {category.featured && (
                    <Badge className="bg-yellow-100 text-yellow-800 text-xs">
                      Popular
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 mb-2">
                  {category.title}
                </CardTitle>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {category.description}
                </p>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{category.cases}</div>
                    <div className="text-xs text-gray-500">Casos</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{category.successRate}</div>
                    <div className="text-xs text-gray-500">Éxito</div>
                  </div>
                </div>
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Costo promedio:</span>
                    <span className="font-medium text-gray-900">{category.avgCost}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Tiempo estimado:</span>
                    <span className="font-medium text-gray-900">{category.timeframe}</span>
                  </div>
                </div>
                <Button 
                  variant="outline" 
                  className="w-full group"
                  onClick={() => setSelectedCategory(category.id)}
                >
                  Ver {category.lawyers} Abogados
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Detailed Service View */}
        {selectedService && (
          <Card className="mb-16 bg-white/80 backdrop-blur-sm shadow-2xl">
            <CardContent className="p-8">
              <div className="grid lg:grid-cols-2 gap-8">
                <div>
                  <div className="flex items-center space-x-4 mb-6">
                    <div className={`w-16 h-16 ${selectedService.lightColor} rounded-2xl flex items-center justify-center`}>
                      <selectedService.icon className={`h-8 w-8 ${selectedService.textColor}`} />
                    </div>
                    <div>
                      <h3 className="text-3xl font-bold text-gray-900">{selectedService.title}</h3>
                      <p className="text-gray-600">{selectedService.description}</p>
                    </div>
                  </div>

                  <div className="space-y-4 mb-6">
                    <h4 className="text-lg font-semibold text-gray-900">Servicios Incluidos:</h4>
                    <div className="grid grid-cols-1 gap-3">
                      {selectedService.services.map((service, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                          <span className="text-gray-700">{service}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Button className="btn-legal">
                    Consulta Gratuita
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-6">
                  <div className="grid grid-cols-2 gap-4">
                    <Card className="p-4 text-center">
                      <div className="text-3xl font-bold text-gray-900 mb-1">{selectedService.cases}</div>
                      <div className="text-sm text-gray-500">Casos Manejados</div>
                    </Card>
                    <Card className="p-4 text-center">
                      <div className="text-3xl font-bold text-green-600 mb-1">{selectedService.successRate}</div>
                      <div className="text-sm text-gray-500">Tasa de Éxito</div>
                    </Card>
                  </div>

                  <Card className="p-6 bg-blue-50">
                    <h4 className="font-semibold text-gray-900 mb-4">Información del Servicio</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Abogados disponibles:</span>
                        <span className="font-medium text-gray-900">{selectedService.lawyers}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Costo promedio:</span>
                        <span className="font-medium text-gray-900">{selectedService.avgCost}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Tiempo estimado:</span>
                        <span className="font-medium text-gray-900">{selectedService.timeframe}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Consulta inicial:</span>
                        <span className="font-medium text-green-600">Gratuita</span>
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-yellow-50">
                    <div className="flex items-center space-x-3 mb-3">
                      <Star className="h-5 w-5 text-yellow-500" />
                      <span className="font-semibold text-gray-900">Garantía de Satisfacción</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      Si no estás satisfecho con tu abogado en los primeros 7 días, 
                      te ayudamos a encontrar otro sin costo adicional.
                    </p>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Process Steps */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Cómo Funciona
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Un proceso simple y transparente para conectarte con el abogado perfecto
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {processSteps.map((step, index) => (
            <Card key={index} className="text-center p-6 card-hover">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <step.icon className="h-8 w-8 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-blue-600 mb-2">{step.step}</div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{step.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
