'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Users, Scale } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function HeroSection() {
  const t = useTranslations('hero');

  return (
    <div className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 pt-32 pb-20 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="w-full h-full bg-slate-800 bg-opacity-40"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:flex lg:items-center lg:justify-between">
          <div className="lg:w-1/2">
            <h1 className="text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl">
              {t.rich('title', {
                highlight: (chunks) => (
                  <span className="bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">
                    {chunks}
                  </span>
                )
              })}
            </h1>
            <p className="mt-6 text-xl text-gray-300 leading-relaxed">
              {t('subtitle')}
            </p>
            <div className="mt-10 flex flex-col sm:flex-row gap-4">
              <Button variant="legal" size="lg" className="shadow-xl hover:shadow-2xl transition-all duration-200 transform hover:-translate-y-1">
                <Users className="mr-2 h-5 w-5" />
                {t('findLawyer')}
              </Button>
              <Button variant="outline" size="lg" className="bg-white text-blue-600 hover:bg-gray-50 shadow-xl hover:shadow-2xl transition-all duration-200 transform hover:-translate-y-1">
                <Scale className="mr-2 h-5 w-5" />
                {t('joinAsLawyer')}
              </Button>
            </div>
          </div>
          <div className="mt-10 lg:mt-0 lg:w-1/2">
            <div className="relative">
              <svg className="w-full max-w-lg mx-auto" viewBox="0 0 500 400" xmlns="http://www.w3.org/2000/svg">
                <defs>
                  <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style={{stopColor: '#3b82f6', stopOpacity: 1}} />
                    <stop offset="100%" style={{stopColor: '#2dd4bf', stopOpacity: 1}} />
                  </linearGradient>
                </defs>
                {/* Puerto Rico Map Outline */}
                <path d="M50,200 C50,150 100,100 200,100 C300,100 350,150 400,150 C450,150 450,200 400,250 C350,300 300,300 200,300 C100,300 50,250 50,200 Z" fill="none" stroke="url(#grad1)" strokeWidth="3" />
                {/* Courthouse */}
                <rect x="200" y="150" width="100" height="100" fill="#1e293b" rx="5" />
                <rect x="220" y="170" width="20" height="80" fill="#334155" />
                <rect x="260" y="170" width="20" height="80" fill="#334155" />
                <rect x="190" y="150" width="120" height="20" fill="#334155" rx="5" />
                <rect x="180" y="140" width="140" height="10" fill="#475569" rx="5" />
                {/* Scales of Justice */}
                <line x1="250" y1="80" x2="250" y2="130" stroke="#e2e8f0" strokeWidth="3" />
                <line x1="220" y1="80" x2="280" y2="80" stroke="#e2e8f0" strokeWidth="3" />
                <circle cx="220" cy="90" r="15" fill="#3b82f6" />
                <circle cx="280" cy="90" r="15" fill="#2dd4bf" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
