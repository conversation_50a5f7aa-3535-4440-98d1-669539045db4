'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Star, 
  MapPin, 
  Clock, 
  Award, 
  Users, 
  Phone, 
  Mail,
  Calendar,
  MessageSquare,
  Filter,
  Search,
  Verified,
  TrendingUp,
  DollarSign,
  Languages,
  GraduationCap,
  Building
} from 'lucide-react';

const lawyers = [
  {
    id: 1,
    name: '<PERSON><PERSON><PERSON> <PERSON>',
    title: 'Especialista en Derecho de Familia',
    image: '/api/placeholder/150/150',
    rating: 4.9,
    reviews: 127,
    experience: 15,
    location: 'San Juan, PR',
    specializations: ['Derecho de Familia', 'Divorcio', 'Custodia'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Puerto Rico, Escuela de Derecho',
    hourlyRate: 250,
    responseTime: '< 2 horas',
    casesWon: 234,
    isVerified: true,
    isOnline: true,
    bio: 'Más de 15 años de experiencia en derecho de familia. Especializada en casos complejos de divorcio y custodia de menores.',
    achievements: ['Top 10 Abogados de Familia 2023', 'Certificada en Mediación Familiar'],
    availability: 'Disponible esta semana'
  },
  {
    id: 2,
    name: 'Lic. Carlos Alberto Rodríguez',
    title: 'Experto en Derecho Inmobiliario',
    image: '/api/placeholder/150/150',
    rating: 4.8,
    reviews: 89,
    experience: 12,
    location: 'Bayamón, PR',
    specializations: ['Derecho Inmobiliario', 'Contratos', 'Títulos'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad Interamericana, Escuela de Derecho',
    hourlyRate: 200,
    responseTime: '< 4 horas',
    casesWon: 156,
    isVerified: true,
    isOnline: false,
    bio: 'Especialista en transacciones inmobiliarias complejas y resolución de disputas de propiedad.',
    achievements: ['Mejor Abogado Inmobiliario 2022', 'Miembro del Colegio de Abogados'],
    availability: 'Próxima cita: Mañana'
  },
  {
    id: 3,
    name: 'Lic. Ana Isabel Martínez',
    title: 'Defensora Penal Experimentada',
    image: '/api/placeholder/150/150',
    rating: 4.7,
    reviews: 203,
    experience: 18,
    location: 'Ponce, PR',
    specializations: ['Derecho Penal', 'Defensa Criminal', 'Apelaciones'],
    languages: ['Español', 'Inglés', 'Francés'],
    education: 'Universidad de Harvard, Escuela de Derecho',
    hourlyRate: 350,
    responseTime: '< 1 hora',
    casesWon: 312,
    isVerified: true,
    isOnline: true,
    bio: 'Defensora penal con amplia experiencia en casos federales y estatales. Graduada de Harvard Law School.',
    achievements: ['Super Lawyer 2023', 'Mejor Defensora Penal del Sur'],
    availability: 'Disponible hoy'
  },
  {
    id: 4,
    name: 'Dr. Roberto Luis Sánchez',
    title: 'Especialista en Derecho Corporativo',
    image: '/api/placeholder/150/150',
    rating: 4.9,
    reviews: 67,
    experience: 20,
    location: 'San Juan, PR',
    specializations: ['Derecho Corporativo', 'M&A', 'Compliance'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Columbia, Escuela de Derecho',
    hourlyRate: 450,
    responseTime: '< 3 horas',
    casesWon: 89,
    isVerified: true,
    isOnline: true,
    bio: 'Experto en fusiones y adquisiciones con más de 20 años asesorando empresas Fortune 500.',
    achievements: ['Chambers & Partners Ranked', 'Legal 500 Recommended'],
    availability: 'Disponible esta semana'
  },
  {
    id: 5,
    name: 'Lic. Carmen Rosa Vega',
    title: 'Abogada Laboral Certificada',
    image: '/api/placeholder/150/150',
    rating: 4.6,
    reviews: 145,
    experience: 10,
    location: 'Mayagüez, PR',
    specializations: ['Derecho Laboral', 'Discriminación', 'Compensación'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Puerto Rico, Escuela de Derecho',
    hourlyRate: 180,
    responseTime: '< 6 horas',
    casesWon: 178,
    isVerified: true,
    isOnline: false,
    bio: 'Defensora apasionada de los derechos de los trabajadores con un historial probado de éxito.',
    achievements: ['Abogada del Año 2021', 'Certificada en Derecho Laboral'],
    availability: 'Próxima cita: Esta tarde'
  },
  {
    id: 6,
    name: 'Dr. Miguel Ángel Torres',
    title: 'Especialista en Lesiones Personales',
    image: '/api/placeholder/150/150',
    rating: 4.8,
    reviews: 234,
    experience: 16,
    location: 'Caguas, PR',
    specializations: ['Lesiones Personales', 'Negligencia Médica', 'Accidentes'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Georgetown, Escuela de Derecho',
    hourlyRate: 0, // Contingency fee
    responseTime: '< 2 horas',
    casesWon: 267,
    isVerified: true,
    isOnline: true,
    bio: 'Especialista en lesiones personales con más de $50M recuperados para clientes.',
    achievements: ['Million Dollar Advocates Forum', 'Top 100 Trial Lawyers'],
    availability: 'Consulta gratuita hoy'
  }
];

const specializations = [
  'Todas las especialidades',
  'Derecho de Familia',
  'Derecho Inmobiliario',
  'Derecho Penal',
  'Derecho Corporativo',
  'Derecho Laboral',
  'Lesiones Personales',
  'Derecho de Inmigración',
  'Derecho Tributario'
];

const locations = [
  'Todas las ubicaciones',
  'San Juan',
  'Bayamón',
  'Ponce',
  'Mayagüez',
  'Caguas',
  'Arecibo',
  'Guaynabo'
];

export default function LawyerDirectory() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState('Todas las especialidades');
  const [selectedLocation, setSelectedLocation] = useState('Todas las ubicaciones');
  const [sortBy, setSortBy] = useState('rating');
  const [showFilters, setShowFilters] = useState(false);

  const filteredLawyers = lawyers.filter(lawyer => {
    const matchesSearch = lawyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lawyer.specializations.some(spec => spec.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesSpecialization = selectedSpecialization === 'Todas las especialidades' ||
                                 lawyer.specializations.includes(selectedSpecialization);
    const matchesLocation = selectedLocation === 'Todas las ubicaciones' ||
                           lawyer.location.includes(selectedLocation);
    
    return matchesSearch && matchesSpecialization && matchesLocation;
  });

  const sortedLawyers = [...filteredLawyers].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return b.rating - a.rating;
      case 'experience':
        return b.experience - a.experience;
      case 'price-low':
        return a.hourlyRate - b.hourlyRate;
      case 'price-high':
        return b.hourlyRate - a.hourlyRate;
      default:
        return 0;
    }
  });

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Directorio de Abogados
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Encuentra el abogado perfecto para tu caso. Todos nuestros profesionales están verificados y licenciados.
          </p>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <Input
                  placeholder="Buscar por nombre o especialidad..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Select value={selectedSpecialization} onValueChange={setSelectedSpecialization}>
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="Especialidad" />
                  </SelectTrigger>
                  <SelectContent>
                    {specializations.map((spec) => (
                      <SelectItem key={spec} value={spec}>{spec}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Ubicación" />
                  </SelectTrigger>
                  <SelectContent>
                    {locations.map((location) => (
                      <SelectItem key={location} value={location}>{location}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-[150px]">
                    <SelectValue placeholder="Ordenar por" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rating">Calificación</SelectItem>
                    <SelectItem value="experience">Experiencia</SelectItem>
                    <SelectItem value="price-low">Precio: Menor</SelectItem>
                    <SelectItem value="price-high">Precio: Mayor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                Mostrando {sortedLawyers.length} de {lawyers.length} abogados
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="lg:hidden"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filtros
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Lawyers Grid */}
        <div className="grid lg:grid-cols-2 xl:grid-cols-3 gap-8">
          {sortedLawyers.map((lawyer) => (
            <Card key={lawyer.id} className="card-hover bg-white">
              <CardHeader className="pb-4">
                <div className="flex items-start space-x-4">
                  <div className="relative">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="h-8 w-8 text-gray-400" />
                    </div>
                    {lawyer.isOnline && (
                      <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                    {lawyer.isVerified && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                        <Verified className="h-3 w-3 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg font-bold text-gray-900 truncate">{lawyer.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">{lawyer.title}</p>
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(lawyer.rating)
                                ? 'text-yellow-400 fill-current'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm font-medium text-gray-900">{lawyer.rating}</span>
                      <span className="text-sm text-gray-500">({lawyer.reviews} reseñas)</span>
                    </div>
                  </div>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Specializations */}
                <div className="flex flex-wrap gap-2">
                  {lawyer.specializations.slice(0, 2).map((spec) => (
                    <Badge key={spec} variant="secondary" className="text-xs">
                      {spec}
                    </Badge>
                  ))}
                  {lawyer.specializations.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{lawyer.specializations.length - 2} más
                    </Badge>
                  )}
                </div>

                {/* Key Stats */}
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Award className="h-4 w-4 text-blue-600" />
                    <span className="text-gray-600">{lawyer.experience} años exp.</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    <span className="text-gray-600">{lawyer.location}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-600" />
                    <span className="text-gray-600">{lawyer.responseTime}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4 text-blue-600" />
                    <span className="text-gray-600">{lawyer.casesWon} casos</span>
                  </div>
                </div>

                {/* Bio */}
                <p className="text-sm text-gray-600 line-clamp-2">{lawyer.bio}</p>

                {/* Pricing */}
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-lg font-bold text-gray-900">
                      {lawyer.hourlyRate === 0 ? 'Sin costo inicial' : `$${lawyer.hourlyRate}/hora`}
                    </div>
                    <div className="text-xs text-green-600">{lawyer.availability}</div>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                    <Button size="sm" className="btn-legal">
                      <Calendar className="h-4 w-4 mr-1" />
                      Cita
                    </Button>
                  </div>
                </div>

                {/* Achievements */}
                {lawyer.achievements.length > 0 && (
                  <div className="pt-3 border-t border-gray-100">
                    <div className="text-xs text-gray-500 mb-1">Reconocimientos:</div>
                    <div className="text-xs text-blue-600">
                      {lawyer.achievements[0]}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Cargar Más Abogados
          </Button>
        </div>
      </div>
    </section>
  );
}
