'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Users, 
  Scale, 
  Award, 
  MapPin, 
  Clock, 
  DollarSign,
  Star,
  CheckCircle,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Zap,
  Shield
} from 'lucide-react';

const platformStats = [
  {
    title: 'Casos Resueltos',
    value: '15,247',
    change: '+12.5%',
    trend: 'up',
    icon: Scale,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50'
  },
  {
    title: 'Abogados Activos',
    value: '1,234',
    change: '+8.2%',
    trend: 'up',
    icon: Users,
    color: 'text-green-600',
    bgColor: 'bg-green-50'
  },
  {
    title: 'Satisfacción Cliente',
    value: '98.7%',
    change: '+2.1%',
    trend: 'up',
    icon: Star,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50'
  },
  {
    title: 'Tiempo Promedio',
    value: '4.2 días',
    change: '-15.3%',
    trend: 'down',
    icon: Clock,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50'
  }
];

const regionalData = [
  { region: 'San Juan', lawyers: 342, cases: 4567, satisfaction: 98.2 },
  { region: 'Bayamón', lawyers: 156, cases: 2134, satisfaction: 97.8 },
  { region: 'Ponce', lawyers: 189, cases: 2891, satisfaction: 98.5 },
  { region: 'Mayagüez', lawyers: 98, cases: 1456, satisfaction: 97.9 },
  { region: 'Caguas', lawyers: 134, cases: 1789, satisfaction: 98.1 },
  { region: 'Arecibo', lawyers: 87, cases: 1234, satisfaction: 97.6 }
];

const specialtyStats = [
  { name: 'Derecho de Familia', percentage: 28, cases: 4269, avgCost: '$2,500' },
  { name: 'Derecho Inmobiliario', percentage: 22, cases: 3354, avgCost: '$1,800' },
  { name: 'Derecho Penal', percentage: 18, cases: 2744, avgCost: '$3,200' },
  { name: 'Derecho Laboral', percentage: 15, cases: 2286, avgCost: '$2,100' },
  { name: 'Derecho Corporativo', percentage: 10, cases: 1524, avgCost: '$4,500' },
  { name: 'Otros', percentage: 7, cases: 1067, avgCost: '$2,800' }
];

const achievements = [
  {
    title: 'Plataforma Legal #1',
    description: 'Reconocida como la mejor plataforma legal de Puerto Rico',
    icon: Award,
    year: '2023'
  },
  {
    title: 'Certificación ISO 27001',
    description: 'Máxima seguridad en protección de datos',
    icon: Shield,
    year: '2023'
  },
  {
    title: 'Premio Innovación Digital',
    description: 'Por transformar el acceso a servicios legales',
    icon: Zap,
    year: '2022'
  },
  {
    title: 'Mejor Startup Legal',
    description: 'Reconocimiento del Colegio de Abogados de PR',
    icon: Target,
    year: '2022'
  }
];

export default function AnalyticsShowcase() {
  const [animatedValues, setAnimatedValues] = useState<Record<string, number>>({});

  useEffect(() => {
    // Animate numbers on mount
    const targets = {
      cases: 15247,
      lawyers: 1234,
      satisfaction: 98.7,
      avgTime: 4.2
    };

    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const timer = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      const easeOut = 1 - Math.pow(1 - progress, 3);

      setAnimatedValues({
        cases: Math.floor(targets.cases * easeOut),
        lawyers: Math.floor(targets.lawyers * easeOut),
        satisfaction: Number((targets.satisfaction * easeOut).toFixed(1)),
        avgTime: Number((targets.avgTime * easeOut).toFixed(1))
      });

      if (currentStep >= steps) {
        clearInterval(timer);
        setAnimatedValues(targets);
      }
    }, stepDuration);

    return () => clearInterval(timer);
  }, []);

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-4 py-2 bg-blue-100 text-blue-800">
            Datos en Tiempo Real
          </Badge>
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Impacto Medible en la
            <span className="legal-text-gradient block">Justicia de Puerto Rico</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Números que demuestran nuestro compromiso con la excelencia legal y la satisfacción del cliente.
          </p>
        </div>

        {/* Main Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {platformStats.map((stat, index) => (
            <Card key={index} className="card-hover bg-white/80 backdrop-blur-sm border-0 shadow-xl">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 ${stat.bgColor} rounded-xl flex items-center justify-center`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <Badge 
                    className={`${
                      stat.trend === 'up' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {stat.change}
                  </Badge>
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {stat.title === 'Casos Resueltos' && animatedValues.cases?.toLocaleString()}
                  {stat.title === 'Abogados Activos' && animatedValues.lawyers?.toLocaleString()}
                  {stat.title === 'Satisfacción Cliente' && `${animatedValues.satisfaction}%`}
                  {stat.title === 'Tiempo Promedio' && `${animatedValues.avgTime} días`}
                </div>
                <div className="text-sm text-gray-600">{stat.title}</div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Regional Performance */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5 text-blue-600" />
                <span>Cobertura Regional</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {regionalData.map((region, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-semibold text-gray-900">{region.region}</div>
                      <div className="text-sm text-gray-600">
                        {region.lawyers} abogados • {region.cases.toLocaleString()} casos
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{region.satisfaction}%</div>
                      <div className="text-xs text-gray-500">Satisfacción</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <PieChart className="h-5 w-5 text-blue-600" />
                <span>Distribución por Especialidad</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {specialtyStats.map((specialty, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-900">{specialty.name}</span>
                      <span className="text-sm text-gray-600">{specialty.percentage}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-1000 ease-out"
                        style={{ width: `${specialty.percentage}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{specialty.cases.toLocaleString()} casos</span>
                      <span>Promedio: {specialty.avgCost}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Success Metrics */}
        <Card className="mb-16 bg-gradient-to-r from-blue-900 to-blue-800 text-white">
          <CardContent className="p-8">
            <div className="grid md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold mb-2">$127M+</div>
                <div className="text-blue-100">Recuperado para clientes</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">24/7</div>
                <div className="text-blue-100">Soporte disponible</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">99.9%</div>
                <div className="text-blue-100">Tiempo de actividad</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Achievements */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Reconocimientos y Certificaciones
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Nuestro compromiso con la excelencia ha sido reconocido por las principales organizaciones del sector legal
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {achievements.map((achievement, index) => (
            <Card key={index} className="text-center p-6 card-hover bg-white/80 backdrop-blur-sm border-0 shadow-xl">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <achievement.icon className="h-8 w-8 text-yellow-600" />
                </div>
                <Badge className="mb-3 bg-blue-100 text-blue-800">{achievement.year}</Badge>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{achievement.title}</h4>
                <p className="text-gray-600 text-sm leading-relaxed">{achievement.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-0">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                ¿Listo para formar parte de estos números?
              </h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                Únete a miles de clientes satisfechos y abogados exitosos en la plataforma legal más confiable de Puerto Rico.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="btn-legal">
                  Buscar Abogado
                </Button>
                <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white">
                  Registrarse como Abogado
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
}
