{"name": "LegalPR", "description": "Puerto Rico Legal Marketplace - Connecting clients with qualified attorneys", "repository": "https://github.com/your-username/delawpr", "logo": "https://your-domain.com/logo.png", "keywords": ["nextjs", "firebase", "stripe", "legal", "puerto-rico"], "image": "hero<PERSON>/nodejs", "stack": "heroku-22", "buildpacks": [{"url": "hero<PERSON>/nodejs"}], "env": {"NODE_ENV": {"description": "Node environment", "value": "production"}, "NEXT_PUBLIC_APP_URL": {"description": "Application URL", "required": true}, "NEXT_PUBLIC_FIREBASE_API_KEY": {"description": "Firebase API Key", "required": true}, "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN": {"description": "Firebase Auth Domain", "required": true}, "NEXT_PUBLIC_FIREBASE_PROJECT_ID": {"description": "Firebase Project ID", "required": true}, "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET": {"description": "Firebase Storage Bucket", "required": true}, "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID": {"description": "Firebase Messaging Sender ID", "required": true}, "NEXT_PUBLIC_FIREBASE_APP_ID": {"description": "Firebase App ID", "required": true}, "FIREBASE_PRIVATE_KEY": {"description": "Firebase Admin Private Key", "required": true}, "FIREBASE_CLIENT_EMAIL": {"description": "Firebase Admin Client Email", "required": true}, "NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY": {"description": "Stripe Publishable Key", "required": true}, "STRIPE_SECRET_KEY": {"description": "Stripe Secret Key", "required": true}, "STRIPE_WEBHOOK_SECRET": {"description": "Stripe Webhook Secret", "required": true}}, "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": ["heroku-postgresql:mini"], "scripts": {"postdeploy": "echo 'Deployment completed successfully'"}}