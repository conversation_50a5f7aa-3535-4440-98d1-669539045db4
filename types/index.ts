import { Timestamp } from 'firebase/firestore';

export type UserRole = 'client' | 'lawyer' | 'admin';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  profileImage?: string;
  phone?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isActive: boolean;
  emailVerified: boolean;
  preferences: {
    language: 'es' | 'en';
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
}

export interface LawyerProfile extends User {
  role: 'lawyer';
  licenseNumber: string;
  barAssociation: string;
  specializations: LegalCategory[];
  bio: string;
  education: Education[];
  experience: Experience[];
  languages: string[];
  hourlyRate: number;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  availability: {
    monday: TimeSlot[];
    tuesday: TimeSlot[];
    wednesday: TimeSlot[];
    thursday: TimeSlot[];
    friday: TimeSlot[];
    saturday: TimeSlot[];
    sunday: TimeSlot[];
  };
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  stripeAccountId?: string;
  stripeOnboardingComplete: boolean;
  documents: {
    license: string;
    insurance: string;
    certifications: string[];
  };
}

export interface ClientProfile extends User {
  role: 'client';
  dateOfBirth?: Timestamp;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string;   // HH:MM format
  isAvailable: boolean;
}

export interface Education {
  institution: string;
  degree: string;
  fieldOfStudy: string;
  graduationYear: number;
}

export interface Experience {
  company: string;
  position: string;
  startDate: Timestamp;
  endDate?: Timestamp;
  description: string;
  isCurrent: boolean;
}

export type LegalCategory = 
  | 'criminal'
  | 'family'
  | 'real-estate'
  | 'immigration'
  | 'corporate'
  | 'personal-injury'
  | 'employment'
  | 'bankruptcy'
  | 'intellectual-property'
  | 'tax'
  | 'estate-planning'
  | 'civil-litigation';

export type AppointmentStatus = 
  | 'pending'
  | 'confirmed'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'no-show';

export interface Appointment {
  id: string;
  clientId: string;
  lawyerId: string;
  title: string;
  description: string;
  category: LegalCategory;
  scheduledDate: Timestamp;
  duration: number; // in minutes
  status: AppointmentStatus;
  meetingType: 'in-person' | 'video' | 'phone';
  location?: string;
  videoLink?: string;
  documents: string[];
  notes: string;
  cost: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  paymentIntentId?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Review {
  id: string;
  clientId: string;
  lawyerId: string;
  appointmentId: string;
  rating: number; // 1-5
  title: string;
  content: string;
  isAnonymous: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  response?: {
    content: string;
    createdAt: Timestamp;
  };
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: Message;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isActive: boolean;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  type: 'text' | 'file' | 'image';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  createdAt: Timestamp;
  readBy: {
    userId: string;
    readAt: Timestamp;
  }[];
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'appointment' | 'message' | 'payment' | 'review' | 'system';
  isRead: boolean;
  actionUrl?: string;
  createdAt: Timestamp;
}

export interface SearchFilters {
  category?: LegalCategory;
  location?: {
    city?: string;
    radius?: number; // in miles
  };
  rating?: number;
  priceRange?: {
    min: number;
    max: number;
  };
  languages?: string[];
  availability?: {
    date: Date;
    time: string;
  };
}

export interface LawyerSearchResult {
  lawyer: LawyerProfile;
  distance?: number;
  nextAvailableSlot?: {
    date: Date;
    time: string;
  };
}
