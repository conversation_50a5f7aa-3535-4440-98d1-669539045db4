import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Provider as PaperProvider } from 'react-native-paper';
import { NavigationContainer } from '@react-navigation/native';
import { AuthProvider } from './lib/auth/AuthProvider';
import { NotificationProvider } from './lib/notifications/NotificationProvider';
import { StripeProvider } from '@stripe/stripe-react-native';
import RootNavigator from './navigation/RootNavigator';
import { theme } from './lib/theme';
import { STRIPE_PUBLISHABLE_KEY } from './config/constants';

export default function App() {
  return (
    <SafeAreaProvider>
      <PaperProvider theme={theme}>
        <StripeProvider publishableKey={STRIPE_PUBLISHABLE_KEY}>
          <AuthProvider>
            <NotificationProvider>
              <NavigationContainer>
                <RootNavigator />
                <StatusBar style="auto" />
              </NavigationContainer>
            </NotificationProvider>
          </AuthProvider>
        </StripeProvider>
      </PaperProvider>
    </SafeAreaProvider>
  );
}
