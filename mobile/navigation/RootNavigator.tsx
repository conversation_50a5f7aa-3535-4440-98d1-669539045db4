import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useAuth } from '../lib/auth/AuthProvider';
import AuthNavigator from './AuthNavigator';
import MainNavigator from './MainNavigator';
import LawyerDetailScreen from '../screens/LawyerDetailScreen';
import AppointmentDetailScreen from '../screens/AppointmentDetailScreen';
import BookAppointmentScreen from '../screens/BookAppointmentScreen';
import ChatScreen from '../screens/ChatScreen';
import ProfileScreen from '../screens/ProfileScreen';
import SettingsScreen from '../screens/SettingsScreen';
import { RootStackParamList } from '../types';
import LoadingScreen from '../screens/LoadingScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function RootNavigator() {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {user ? (
        <>
          <Stack.Screen name="Main" component={MainNavigator} />
          <Stack.Screen 
            name="LawyerDetail" 
            component={LawyerDetailScreen}
            options={{ 
              headerShown: true,
              title: 'Perfil del Abogado',
              headerBackTitle: 'Atrás'
            }}
          />
          <Stack.Screen 
            name="AppointmentDetail" 
            component={AppointmentDetailScreen}
            options={{ 
              headerShown: true,
              title: 'Detalles de la Cita',
              headerBackTitle: 'Atrás'
            }}
          />
          <Stack.Screen 
            name="BookAppointment" 
            component={BookAppointmentScreen}
            options={{ 
              headerShown: true,
              title: 'Programar Cita',
              headerBackTitle: 'Atrás'
            }}
          />
          <Stack.Screen 
            name="Chat" 
            component={ChatScreen}
            options={{ 
              headerShown: true,
              title: 'Conversación',
              headerBackTitle: 'Atrás'
            }}
          />
          <Stack.Screen 
            name="Profile" 
            component={ProfileScreen}
            options={{ 
              headerShown: true,
              title: 'Mi Perfil',
              headerBackTitle: 'Atrás'
            }}
          />
          <Stack.Screen 
            name="Settings" 
            component={SettingsScreen}
            options={{ 
              headerShown: true,
              title: 'Configuración',
              headerBackTitle: 'Atrás'
            }}
          />
        </>
      ) : (
        <Stack.Screen name="Auth" component={AuthNavigator} />
      )}
    </Stack.Navigator>
  );
}
