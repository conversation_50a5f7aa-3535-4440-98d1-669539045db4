import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ScrollView, RefreshControl } from 'react-native';
import { Text, Card, Button, Avatar, Chip } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '../../lib/auth/AuthProvider';
import { COLORS, LEGAL_CATEGORIES } from '../../config/constants';

export default function HomeScreen() {
  const { user } = useAuth();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Buenos días';
    if (hour < 18) return 'Buenas tardes';
    return 'Buenas noches';
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView 
        contentContainerStyle={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.greeting}>
            {getGreeting()}, {user?.firstName}!
          </Text>
          <Text style={styles.subtitle}>
            ¿En qué podemos ayudarte hoy?
          </Text>
        </View>

        {/* Quick Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Acciones Rápidas</Text>
            <View style={styles.quickActions}>
              <Button
                mode="contained"
                icon="magnify"
                style={styles.actionButton}
                onPress={() => {}}
              >
                Buscar Abogados
              </Button>
              <Button
                mode="outlined"
                icon="calendar-plus"
                style={styles.actionButton}
                onPress={() => {}}
              >
                Nueva Cita
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Legal Categories */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Áreas Legales</Text>
            <Text style={styles.cardSubtitle}>
              Encuentra abogados especializados
            </Text>
            <View style={styles.categoriesContainer}>
              {LEGAL_CATEGORIES.slice(0, 6).map((category) => (
                <Chip
                  key={category.id}
                  mode="outlined"
                  style={styles.categoryChip}
                  onPress={() => {}}
                >
                  {category.name}
                </Chip>
              ))}
            </View>
            <Button
              mode="text"
              onPress={() => {}}
              style={styles.seeAllButton}
            >
              Ver todas las áreas
            </Button>
          </Card.Content>
        </Card>

        {/* Recent Activity */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Actividad Reciente</Text>
            <View style={styles.activityItem}>
              <Avatar.Icon size={40} icon="calendar" style={styles.activityIcon} />
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>
                  Cita programada con Dra. María González
                </Text>
                <Text style={styles.activitySubtitle}>
                  Mañana a las 2:00 PM
                </Text>
              </View>
            </View>
            <View style={styles.activityItem}>
              <Avatar.Icon size={40} icon="message" style={styles.activityIcon} />
              <View style={styles.activityContent}>
                <Text style={styles.activityTitle}>
                  Nuevo mensaje de Lic. Carlos Rodríguez
                </Text>
                <Text style={styles.activitySubtitle}>
                  Hace 2 horas
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Featured Lawyers */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>Abogados Destacados</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {[1, 2, 3].map((lawyer) => (
                <Card key={lawyer} style={styles.lawyerCard}>
                  <Card.Content style={styles.lawyerContent}>
                    <Avatar.Text size={60} label="MG" style={styles.lawyerAvatar} />
                    <Text style={styles.lawyerName}>Dra. María González</Text>
                    <Text style={styles.lawyerSpecialty}>Derecho de Familia</Text>
                    <View style={styles.ratingContainer}>
                      <Text style={styles.rating}>⭐ 4.9</Text>
                      <Text style={styles.reviews}>(47 reseñas)</Text>
                    </View>
                  </Card.Content>
                </Card>
              ))}
            </ScrollView>
          </Card.Content>
        </Card>

        {/* Tips Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.cardTitle}>💡 Consejo Legal del Día</Text>
            <Text style={styles.tipText}>
              Siempre revisa los contratos antes de firmarlos. Si tienes dudas, 
              consulta con un abogado especializado en derecho contractual.
            </Text>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.surface,
  },
  content: {
    paddingHorizontal: 16,
    paddingBottom: 20,
  },
  welcomeSection: {
    paddingVertical: 20,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
  },
  card: {
    marginBottom: 16,
    backgroundColor: COLORS.background,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 8,
  },
  cardSubtitle: {
    fontSize: 14,
    color: COLORS.textSecondary,
    marginBottom: 12,
  },
  quickActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryChip: {
    marginBottom: 8,
  },
  seeAllButton: {
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  activityIcon: {
    backgroundColor: COLORS.primary,
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.text,
    marginBottom: 2,
  },
  activitySubtitle: {
    fontSize: 12,
    color: COLORS.textSecondary,
  },
  lawyerCard: {
    width: 160,
    marginRight: 12,
  },
  lawyerContent: {
    alignItems: 'center',
  },
  lawyerAvatar: {
    backgroundColor: COLORS.primary,
    marginBottom: 8,
  },
  lawyerName: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: 4,
  },
  lawyerSpecialty: {
    fontSize: 12,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 12,
    fontWeight: '500',
  },
  reviews: {
    fontSize: 10,
    color: COLORS.textSecondary,
  },
  tipText: {
    fontSize: 14,
    color: COLORS.text,
    lineHeight: 20,
    fontStyle: 'italic',
  },
});
