import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Button, TextInput, Text, Divider, SegmentedButtons } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { auth, db } from '../../lib/firebase';
import { AuthStackParamList, UserRole } from '../../types';
import { COLORS } from '../../config/constants';

type SignUpScreenNavigationProp = NativeStackNavigationProp<AuthStackParamList, 'SignUp'>;

interface Props {
  navigation: SignUpScreenNavigationProp;
}

export default function SignUpScreen({ navigation }: Props) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    userType: 'client' as UserRole
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    if (!formData.firstName.trim()) {
      Alert.alert('Error', 'El nombre es requerido');
      return false;
    }
    if (!formData.lastName.trim()) {
      Alert.alert('Error', 'El apellido es requerido');
      return false;
    }
    if (!formData.email.trim()) {
      Alert.alert('Error', 'El correo electrónico es requerido');
      return false;
    }
    if (formData.password.length < 6) {
      Alert.alert('Error', 'La contraseña debe tener al menos 6 caracteres');
      return false;
    }
    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Las contraseñas no coinciden');
      return false;
    }
    return true;
  };

  const handleSignUp = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      // Create user account
      const userCredential = await createUserWithEmailAndPassword(
        auth, 
        formData.email, 
        formData.password
      );
      const user = userCredential.user;

      // Create user document in Firestore
      await setDoc(doc(db, 'users', user.uid), {
        id: user.uid,
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        role: formData.userType,
        profileImage: '',
        phone: '',
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        emailVerified: false,
        preferences: {
          language: 'es',
          notifications: {
            email: true,
            sms: false,
            push: true
          }
        }
      });

      // If user is a lawyer, create lawyer profile
      if (formData.userType === 'lawyer') {
        await setDoc(doc(db, 'lawyers', user.uid), {
          id: user.uid,
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          role: 'lawyer',
          licenseNumber: '',
          barAssociation: 'Colegio de Abogados de Puerto Rico',
          specializations: [],
          bio: '',
          education: [],
          experience: [],
          languages: ['es'],
          hourlyRate: 0,
          location: {
            address: '',
            city: '',
            state: 'PR',
            zipCode: '',
            coordinates: { lat: 0, lng: 0 }
          },
          rating: 0,
          reviewCount: 0,
          isVerified: false,
          stripeAccountId: '',
          stripeOnboardingComplete: false,
          profileImage: '',
          phone: '',
          createdAt: new Date(),
          updatedAt: new Date(),
          isActive: true,
          emailVerified: false,
          preferences: {
            language: 'es',
            notifications: {
              email: true,
              sms: false,
              push: true
            }
          }
        });
      }

      Alert.alert('¡Éxito!', 'Tu cuenta ha sido creada exitosamente');
    } catch (error: any) {
      let errorMessage = 'Error al crear la cuenta';
      
      switch (error.code) {
        case 'auth/email-already-in-use':
          errorMessage = 'Ya existe una cuenta con este correo electrónico';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Correo electrónico inválido';
          break;
        case 'auth/weak-password':
          errorMessage = 'La contraseña es muy débil';
          break;
        default:
          errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>Crear Cuenta</Text>
          <Text style={styles.subtitle}>Únete a la comunidad legal de Puerto Rico</Text>
        </View>

        <View style={styles.form}>
          {/* User Type Selection */}
          <Text style={styles.sectionTitle}>Tipo de Usuario</Text>
          <SegmentedButtons
            value={formData.userType}
            onValueChange={(value) => handleInputChange('userType', value)}
            buttons={[
              { value: 'client', label: 'Cliente' },
              { value: 'lawyer', label: 'Abogado' }
            ]}
            style={styles.segmentedButtons}
          />

          <View style={styles.nameContainer}>
            <TextInput
              label="Nombre"
              value={formData.firstName}
              onChangeText={(value) => handleInputChange('firstName', value)}
              mode="outlined"
              style={[styles.input, styles.nameInput]}
              left={<TextInput.Icon icon="account" />}
            />
            <TextInput
              label="Apellido"
              value={formData.lastName}
              onChangeText={(value) => handleInputChange('lastName', value)}
              mode="outlined"
              style={[styles.input, styles.nameInput]}
            />
          </View>

          <TextInput
            label="Correo Electrónico"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            mode="outlined"
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            style={styles.input}
            left={<TextInput.Icon icon="email" />}
          />

          <TextInput
            label="Contraseña"
            value={formData.password}
            onChangeText={(value) => handleInputChange('password', value)}
            mode="outlined"
            secureTextEntry={!showPassword}
            autoComplete="password"
            style={styles.input}
            left={<TextInput.Icon icon="lock" />}
            right={
              <TextInput.Icon 
                icon={showPassword ? "eye-off" : "eye"} 
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />

          <TextInput
            label="Confirmar Contraseña"
            value={formData.confirmPassword}
            onChangeText={(value) => handleInputChange('confirmPassword', value)}
            mode="outlined"
            secureTextEntry={!showConfirmPassword}
            style={styles.input}
            left={<TextInput.Icon icon="lock" />}
            right={
              <TextInput.Icon 
                icon={showConfirmPassword ? "eye-off" : "eye"} 
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              />
            }
          />

          <Button
            mode="contained"
            onPress={handleSignUp}
            loading={loading}
            disabled={loading}
            style={styles.signUpButton}
            contentStyle={styles.buttonContent}
          >
            Crear Cuenta
          </Button>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            ¿Ya tienes una cuenta?{' '}
            <Text 
              style={styles.linkText}
              onPress={() => navigation.navigate('SignIn')}
            >
              Inicia sesión aquí
            </Text>
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  content: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: COLORS.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
  },
  form: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: 12,
  },
  segmentedButtons: {
    marginBottom: 24,
  },
  nameContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  nameInput: {
    flex: 1,
  },
  input: {
    marginBottom: 16,
  },
  signUpButton: {
    backgroundColor: COLORS.primary,
    marginTop: 8,
    marginBottom: 24,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: COLORS.textSecondary,
  },
  linkText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
});
