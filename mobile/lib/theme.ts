import { MD3LightTheme as DefaultTheme } from 'react-native-paper';
import { COLORS } from '../config/constants';

export const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: COLORS.primary,
    secondary: COLORS.secondary,
    tertiary: COLORS.success,
    surface: COLORS.surface,
    background: COLORS.background,
    error: COLORS.error,
    onPrimary: '#ffffff',
    onSecondary: '#ffffff',
    onSurface: COLORS.text,
    onBackground: COLORS.text,
    outline: COLORS.border,
  },
  fonts: {
    ...DefaultTheme.fonts,
    displayLarge: {
      ...DefaultTheme.fonts.displayLarge,
      fontWeight: '700',
    },
    displayMedium: {
      ...DefaultTheme.fonts.displayMedium,
      fontWeight: '600',
    },
    headlineLarge: {
      ...DefaultTheme.fonts.headlineLarge,
      fontWeight: '600',
    },
    headlineMedium: {
      ...DefaultTheme.fonts.headlineMedium,
      fontWeight: '600',
    },
  },
};
