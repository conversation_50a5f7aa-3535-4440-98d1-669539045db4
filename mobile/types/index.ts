export type UserRole = 'client' | 'lawyer' | 'admin';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  profileImage?: string;
  phone?: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  emailVerified: boolean;
  preferences: {
    language: 'es' | 'en';
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
}

export interface LawyerProfile extends User {
  role: 'lawyer';
  licenseNumber: string;
  barAssociation: string;
  specializations: string[];
  bio: string;
  education: Education[];
  experience: Experience[];
  languages: string[];
  hourlyRate: number;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  stripeAccountId?: string;
  stripeOnboardingComplete: boolean;
}

export interface Education {
  institution: string;
  degree: string;
  fieldOfStudy: string;
  graduationYear: number;
}

export interface Experience {
  company: string;
  position: string;
  startDate: Date;
  endDate?: Date;
  description: string;
  isCurrent: boolean;
}

export type AppointmentStatus = 
  | 'pending'
  | 'confirmed'
  | 'in-progress'
  | 'completed'
  | 'cancelled'
  | 'no-show';

export interface Appointment {
  id: string;
  clientId: string;
  lawyerId: string;
  title: string;
  description: string;
  category: string;
  scheduledDate: Date;
  duration: number;
  status: AppointmentStatus;
  meetingType: 'in-person' | 'video' | 'phone';
  location?: string;
  videoLink?: string;
  documents: string[];
  notes: string;
  cost: number;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  paymentIntentId?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  type: 'text' | 'file' | 'image';
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  createdAt: Date;
  readBy: {
    userId: string;
    readAt: Date;
  }[];
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: Message;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
}

export interface Review {
  id: string;
  clientId: string;
  lawyerId: string;
  appointmentId: string;
  rating: number;
  title: string;
  content: string;
  isAnonymous: boolean;
  createdAt: Date;
  updatedAt: Date;
  response?: {
    content: string;
    createdAt: Date;
  };
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'appointment' | 'message' | 'payment' | 'review' | 'system';
  isRead: boolean;
  actionUrl?: string;
  createdAt: Date;
}

// Navigation Types
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  LawyerDetail: { lawyerId: string };
  AppointmentDetail: { appointmentId: string };
  BookAppointment: { lawyerId: string };
  Chat: { conversationId: string };
  Profile: undefined;
  Settings: undefined;
};

export type AuthStackParamList = {
  Welcome: undefined;
  SignIn: undefined;
  SignUp: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Search: undefined;
  Appointments: undefined;
  Messages: undefined;
  Profile: undefined;
};
