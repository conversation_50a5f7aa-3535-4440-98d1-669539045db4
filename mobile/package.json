{"name": "delawpr-mobile", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "update": "eas update"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/netinfo": "9.3.10", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@stripe/stripe-react-native": "^0.35.0", "expo": "~49.0.15", "expo-application": "~5.4.0", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-document-picker": "~11.5.4", "expo-file-system": "~15.4.5", "expo-font": "~11.4.0", "expo-image-picker": "~14.3.2", "expo-linking": "~5.0.2", "expo-notifications": "~0.20.1", "expo-router": "^2.0.0", "expo-secure-store": "~12.3.1", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "expo-updates": "~0.18.19", "firebase": "^10.7.1", "react": "18.2.0", "react-native": "0.72.6", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.11.6", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "^0.72.8", "typescript": "^5.1.3"}, "private": true}