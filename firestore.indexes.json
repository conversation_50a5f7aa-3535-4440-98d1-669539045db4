{"indexes": [{"collectionGroup": "lawyers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "specializations", "arrayConfig": "CONTAINS"}, {"fieldPath": "location.city", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "lawyers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "isVerified", "order": "ASCENDING"}, {"fieldPath": "rating", "order": "DESCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "scheduledDate", "order": "ASCENDING"}]}, {"collectionGroup": "appointments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "scheduledDate", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lawyerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "messages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "conversationId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}], "fieldOverrides": []}