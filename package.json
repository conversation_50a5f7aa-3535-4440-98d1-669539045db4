{"name": "delawpr", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "firebase": "^10.7.1", "lucide-react": "^0.294.0", "next": "^14.2.30", "next-intl": "^3.4.0", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "stripe": "^14.9.0", "tailwind-merge": "^2.1.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}